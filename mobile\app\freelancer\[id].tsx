import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useState } from "react";
import {
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { Button, Card } from "../../src/components/ui";
import {
  BorderRadius,
  FontSizes,
  Spacing,
  TunaWorkColors,
} from "../../src/constants";
import { useTranslation } from "../../src/i18n";
import { featuredFreelancers } from "../../src/lib/data";

export default function FreelancerDetailScreen() {
  const { t } = useTranslation();
  const { id } = useLocalSearchParams();
  const [activeTab, setActiveTab] = useState("about");

  // Find the freelancer by ID
  const freelancer = featuredFreelancers.find((f) => f.id === id);

  if (!freelancer) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Freelancer non trouvé</Text>
          <Button title="Retour" onPress={() => router.back()} />
        </View>
      </SafeAreaView>
    );
  }

  const tabs = [
    { key: "about", label: t("profile.about"), icon: "person" },
    { key: "portfolio", label: t("profile.portfolio"), icon: "briefcase" },
    { key: "reviews", label: t("profile.reviews"), icon: "star" },
  ];

  const renderPortfolioItem = ({
    item,
  }: {
    item: (typeof freelancer.portfolio)[0];
  }) => (
    <Card style={styles.portfolioCard} padding="md">
      <Image source={{ uri: item.image }} style={styles.portfolioImage} />
      <Text style={styles.portfolioTitle}>{item.title}</Text>
      <Text style={styles.portfolioDescription}>{item.description}</Text>
    </Card>
  );

  const renderReview = ({ item }: { item: (typeof freelancer.reviews)[0] }) => (
    <Card style={styles.reviewCard} padding="md">
      <View style={styles.reviewHeader}>
        <Text style={styles.reviewerName}>{item.clientName}</Text>
        <View style={styles.reviewRating}>
          {[...Array(5)].map((_, index) => (
            <Ionicons
              key={index}
              name="star"
              size={14}
              color={
                index < item.rating ? "#F59E0B" : TunaWorkColors.secondary[300]
              }
            />
          ))}
        </View>
      </View>
      <Text style={styles.reviewProject}>{item.projectTitle}</Text>
      <Text style={styles.reviewComment}>{item.comment}</Text>
      <Text style={styles.reviewDate}>
        {new Date(item.date).toLocaleDateString("fr-FR")}
      </Text>
    </Card>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case "about":
        return (
          <View style={styles.tabContent}>
            <Text style={styles.description}>{freelancer.description}</Text>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>{t("profile.skills")}</Text>
              <View style={styles.skillsContainer}>
                {freelancer.skills.map((skill, index) => (
                  <View key={index} style={styles.skillTag}>
                    <Text style={styles.skillText}>{skill}</Text>
                  </View>
                ))}
              </View>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Langues</Text>
              <View style={styles.languagesContainer}>
                {freelancer.languages.map((language, index) => (
                  <Text key={index} style={styles.languageText}>
                    • {language}
                  </Text>
                ))}
              </View>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Expérience</Text>
              <Text style={styles.experienceText}>{freelancer.experience}</Text>
            </View>
          </View>
        );

      case "portfolio":
        return (
          <FlatList
            key="portfolio-grid"
            data={freelancer.portfolio}
            renderItem={renderPortfolioItem}
            keyExtractor={(item) => item.id}
            numColumns={2}
            columnWrapperStyle={styles.portfolioRow}
            contentContainerStyle={styles.portfolioContainer}
          />
        );

      case "reviews":
        return (
          <FlatList
            key="reviews-list"
            data={freelancer.reviews}
            renderItem={renderReview}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.reviewsContainer}
          />
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons
            name="arrow-back"
            size={24}
            color={TunaWorkColors.secondary[900]}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profil</Text>
        <TouchableOpacity style={styles.shareButton}>
          <Ionicons
            name="share-outline"
            size={24}
            color={TunaWorkColors.secondary[600]}
          />
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Freelancer Info */}
        <Card style={styles.freelancerCard} padding="lg">
          <View style={styles.freelancerHeader}>
            <View style={styles.avatarContainer}>
              <Image
                source={{ uri: freelancer.avatar }}
                style={styles.avatar}
              />
              <View
                style={[
                  styles.onlineIndicator,
                  {
                    backgroundColor: freelancer.isOnline
                      ? "#10B981"
                      : "#6B7280",
                  },
                ]}
              />
            </View>
            <View style={styles.freelancerInfo}>
              <Text style={styles.freelancerName}>
                {freelancer.firstName} {freelancer.lastName}
              </Text>
              <Text style={styles.freelancerTitle}>{freelancer.title}</Text>
              <Text style={styles.location}>
                <Ionicons
                  name="location"
                  size={14}
                  color={TunaWorkColors.secondary[500]}
                />{" "}
                {freelancer.location}
              </Text>
            </View>
          </View>

          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={16} color="#F59E0B" />
                <Text style={styles.rating}>{freelancer.rating}</Text>
              </View>
              <Text style={styles.statLabel}>
                ({freelancer.reviewCount} avis)
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {freelancer.completedProjects}
              </Text>
              <Text style={styles.statLabel}>Projets terminés</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{freelancer.successRate}%</Text>
              <Text style={styles.statLabel}>Taux de succès</Text>
            </View>
          </View>

          <View style={styles.rateContainer}>
            <Text style={styles.hourlyRate}>
              ${freelancer.hourlyRate}/heure
            </Text>
            <Text style={styles.responseTime}>
              Répond en {freelancer.responseTime}
            </Text>
          </View>
        </Card>

        {/* Tabs */}
        <View style={styles.tabsContainer}>
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[styles.tab, activeTab === tab.key && styles.activeTab]}
              onPress={() => setActiveTab(tab.key)}
            >
              <Ionicons
                name={tab.icon as any}
                size={20}
                color={
                  activeTab === tab.key
                    ? TunaWorkColors.primary[500]
                    : TunaWorkColors.secondary[500]
                }
              />
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab.key && styles.activeTabText,
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Tab Content */}
        {renderTabContent()}
      </ScrollView>

      {/* Contact Button */}
      <View style={styles.contactContainer}>
        <Button
          title={t("professionals.contact")}
          onPress={() => router.push("/auth/login")}
          leftIcon={<Ionicons name="chatbubble" size={20} color="#FFFFFF" />}
          fullWidth
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: TunaWorkColors.secondary[50],
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    backgroundColor: "#FFFFFF",
  },
  backButton: {
    padding: Spacing.sm,
  },
  headerTitle: {
    fontSize: FontSizes.xl,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
  },
  shareButton: {
    padding: Spacing.sm,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: Spacing.lg,
  },
  errorText: {
    fontSize: FontSizes.lg,
    color: TunaWorkColors.secondary[600],
    marginBottom: Spacing.lg,
  },
  freelancerCard: {
    margin: Spacing.md,
  },
  freelancerHeader: {
    flexDirection: "row",
    marginBottom: Spacing.md,
  },
  avatarContainer: {
    position: "relative",
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  onlineIndicator: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 3,
    borderColor: "#FFFFFF",
  },
  freelancerInfo: {
    flex: 1,
    marginLeft: Spacing.md,
  },
  freelancerName: {
    fontSize: FontSizes["2xl"],
    fontWeight: "700",
    color: TunaWorkColors.secondary[900],
  },
  freelancerTitle: {
    fontSize: FontSizes.lg,
    color: TunaWorkColors.secondary[600],
    marginTop: 4,
  },
  location: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[500],
    marginTop: 4,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingVertical: Spacing.md,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: TunaWorkColors.secondary[200],
  },
  statItem: {
    alignItems: "center",
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  rating: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
    marginLeft: 4,
  },
  statValue: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
  },
  statLabel: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.secondary[500],
    textAlign: "center",
    marginTop: 2,
  },
  rateContainer: {
    alignItems: "center",
    paddingTop: Spacing.md,
  },
  hourlyRate: {
    fontSize: FontSizes["2xl"],
    fontWeight: "700",
    color: TunaWorkColors.primary[500],
  },
  responseTime: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
    marginTop: 4,
  },
  tabsContainer: {
    flexDirection: "row",
    backgroundColor: "#FFFFFF",
    marginHorizontal: Spacing.md,
    borderRadius: BorderRadius.xl,
    padding: 4,
    marginBottom: Spacing.md,
  },
  tab: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
    gap: Spacing.xs,
  },
  activeTab: {
    backgroundColor: TunaWorkColors.primary[50],
  },
  tabText: {
    fontSize: FontSizes.sm,
    fontWeight: "500",
    color: TunaWorkColors.secondary[500],
  },
  activeTabText: {
    color: TunaWorkColors.primary[500],
  },
  tabContent: {
    paddingHorizontal: Spacing.md,
  },
  description: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[700],
    lineHeight: 24,
    marginBottom: Spacing.lg,
  },
  section: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
    marginBottom: Spacing.sm,
  },
  skillsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Spacing.xs,
  },
  skillTag: {
    backgroundColor: TunaWorkColors.primary[100],
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  skillText: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.primary[700],
    fontWeight: "500",
  },
  languagesContainer: {
    gap: Spacing.xs,
  },
  languageText: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[700],
  },
  experienceText: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[700],
  },
  portfolioContainer: {
    padding: Spacing.md,
  },
  portfolioRow: {
    justifyContent: "space-between",
  },
  portfolioCard: {
    width: "48%",
    marginBottom: Spacing.md,
  },
  portfolioImage: {
    width: "100%",
    height: 120,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.sm,
  },
  portfolioTitle: {
    fontSize: FontSizes.base,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
    marginBottom: Spacing.xs,
  },
  portfolioDescription: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
    lineHeight: 18,
  },
  reviewsContainer: {
    padding: Spacing.md,
  },
  reviewCard: {
    marginBottom: Spacing.md,
  },
  reviewHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Spacing.xs,
  },
  reviewerName: {
    fontSize: FontSizes.base,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
  },
  reviewRating: {
    flexDirection: "row",
    gap: 2,
  },
  reviewProject: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.primary[600],
    marginBottom: Spacing.xs,
  },
  reviewComment: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[700],
    lineHeight: 22,
    marginBottom: Spacing.xs,
  },
  reviewDate: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.secondary[500],
  },
  contactContainer: {
    padding: Spacing.md,
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: TunaWorkColors.secondary[200],
  },
});
