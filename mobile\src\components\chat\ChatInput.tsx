import { Ionicons } from "@expo/vector-icons";
import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Animated,
  Alert,
} from "react-native";

import {
  BorderRadius,
  FontSizes,
  Spacing,
  TunaWorkColors,
} from "../../constants";

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  onAttachFile?: () => void;
  onSendVoice?: () => void;
  placeholder?: string;
  maxLength?: number;
}

export function ChatInput({
  onSendMessage,
  onAttachFile,
  onSendVoice,
  placeholder = "Tapez votre message...",
  maxLength = 1000,
}: ChatInputProps) {
  const [message, setMessage] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const scaleValue = new Animated.Value(1);

  const handleSend = () => {
    if (message.trim()) {
      onSendMessage(message.trim());
      setMessage("");
      
      // Animation du bouton d'envoi
      Animated.sequence([
        Animated.timing(scaleValue, {
          toValue: 1.2,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  const handleVoicePress = () => {
    if (isRecording) {
      setIsRecording(false);
      onSendVoice?.();
    } else {
      setIsRecording(true);
      // Simuler l'enregistrement vocal
      setTimeout(() => {
        setIsRecording(false);
        Alert.alert("Enregistrement", "Message vocal envoyé !");
      }, 2000);
    }
  };

  const handleAttach = () => {
    Alert.alert(
      "Joindre un fichier",
      "Que souhaitez-vous joindre ?",
      [
        { text: "Photo", onPress: () => onAttachFile?.() },
        { text: "Document", onPress: () => onAttachFile?.() },
        { text: "Annuler", style: "cancel" },
      ]
    );
  };

  return (
    <View style={styles.container}>
      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity style={styles.quickActionButton}>
          <Ionicons name="camera" size={20} color={TunaWorkColors.primary[500]} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.quickActionButton}>
          <Ionicons name="image" size={20} color={TunaWorkColors.primary[500]} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.quickActionButton}>
          <Ionicons name="document" size={20} color={TunaWorkColors.primary[500]} />
        </TouchableOpacity>
      </View>

      {/* Input Row */}
      <View style={styles.inputRow}>
        <TouchableOpacity style={styles.attachButton} onPress={handleAttach}>
          <Ionicons name="add" size={24} color={TunaWorkColors.primary[500]} />
        </TouchableOpacity>
        
        <View style={styles.textInputContainer}>
          <TextInput
            style={styles.textInput}
            placeholder={placeholder}
            placeholderTextColor={TunaWorkColors.secondary[400]}
            value={message}
            onChangeText={setMessage}
            multiline
            maxLength={maxLength}
            textAlignVertical="top"
          />
          {message.length > 0 && (
            <Text style={styles.characterCount}>
              {message.length}/{maxLength}
            </Text>
          )}
        </View>
        
        {message.trim() ? (
          <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
            <TouchableOpacity 
              style={styles.sendButton}
              onPress={handleSend}
            >
              <Ionicons name="send" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </Animated.View>
        ) : (
          <TouchableOpacity 
            style={[
              styles.voiceButton,
              isRecording && styles.voiceButtonRecording
            ]}
            onPress={handleVoicePress}
          >
            <Ionicons 
              name={isRecording ? "stop" : "mic"} 
              size={20} 
              color={isRecording ? "#FFFFFF" : TunaWorkColors.primary[500]} 
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Recording Indicator */}
      {isRecording && (
        <View style={styles.recordingIndicator}>
          <View style={styles.recordingDot} />
          <Text style={styles.recordingText}>Enregistrement en cours...</Text>
          <Text style={styles.recordingTime}>00:02</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: TunaWorkColors.secondary[100],
    paddingBottom: 8,
  },
  quickActions: {
    flexDirection: "row",
    paddingHorizontal: Spacing.md,
    paddingTop: Spacing.sm,
    gap: Spacing.sm,
  },
  quickActionButton: {
    padding: Spacing.xs,
    backgroundColor: TunaWorkColors.secondary[50],
    borderRadius: BorderRadius.md,
  },
  inputRow: {
    flexDirection: "row",
    alignItems: "flex-end",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    gap: Spacing.sm,
  },
  attachButton: {
    padding: Spacing.sm,
    backgroundColor: TunaWorkColors.secondary[50],
    borderRadius: BorderRadius.lg,
  },
  textInputContainer: {
    flex: 1,
    backgroundColor: TunaWorkColors.secondary[50],
    borderRadius: BorderRadius.xl,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    maxHeight: 120,
    position: "relative",
  },
  textInput: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[900],
    minHeight: 20,
  },
  characterCount: {
    position: "absolute",
    bottom: 4,
    right: 8,
    fontSize: FontSizes.xs,
    color: TunaWorkColors.secondary[400],
  },
  sendButton: {
    padding: Spacing.sm,
    backgroundColor: TunaWorkColors.primary[500],
    borderRadius: BorderRadius.lg,
    shadowColor: TunaWorkColors.primary[500],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  voiceButton: {
    padding: Spacing.sm,
    backgroundColor: TunaWorkColors.secondary[100],
    borderRadius: BorderRadius.lg,
  },
  voiceButtonRecording: {
    backgroundColor: "#EF4444",
  },
  recordingIndicator: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingBottom: Spacing.sm,
    gap: Spacing.sm,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#EF4444",
  },
  recordingText: {
    flex: 1,
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
  },
  recordingTime: {
    fontSize: FontSizes.sm,
    color: "#EF4444",
    fontWeight: "600",
  },
});
