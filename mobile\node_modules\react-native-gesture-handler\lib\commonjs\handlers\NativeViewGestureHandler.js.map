{"version": 3, "sources": ["NativeViewGestureHandler.ts"], "names": ["nativeViewGestureHandlerProps", "nativeViewProps", "baseGestureHandlerProps", "nativeViewHandlerName", "NativeViewGestureHandler", "name", "allowedProps", "config"], "mappings": ";;;;;;;AACA;;AACA;;;;AAKO,MAAMA,6BAA6B,GAAG,CAC3C,uBAD2C,EAE3C,sBAF2C,CAAtC;;AA4BA,MAAMC,eAAe,GAAG,CAC7B,GAAGC,6CAD0B,EAE7B,GAAGF,6BAF0B,CAAxB;;AAKA,MAAMG,qBAAqB,GAAG,0BAA9B;AAEP;AACA;AACA;;;;AAGA;AACA;AACA;AACA;AACO,MAAMC,wBAAwB,GAAG,4BAGtC;AACAC,EAAAA,IAAI,EAAEF,qBADN;AAEAG,EAAAA,YAAY,EAAEL,eAFd;AAGAM,EAAAA,MAAM,EAAE;AAHR,CAHsC,CAAjC", "sourcesContent": ["import type { NativeViewGestureHandlerPayload } from './GestureHandlerEventPayload';\nimport createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\nexport const nativeViewGestureHandlerProps = [\n  'shouldActivateOnStart',\n  'disallowInterruption',\n] as const;\n\nexport interface NativeViewGestureConfig {\n  /**\n   * Android only.\n   *\n   * Determines whether the handler should check for an existing touch event on\n   * instantiation.\n   */\n  shouldActivateOnStart?: boolean;\n\n  /**\n   * When `true`, cancels all other gesture handlers when this\n   * `NativeViewGestureHandler` receives an `ACTIVE` state event.\n   */\n  disallowInterruption?: boolean;\n}\n\n/**\n * @deprecated NativeViewGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Native()` instead.\n */\nexport interface NativeViewGestureHandlerProps\n  extends BaseGestureHandlerProps<NativeViewGestureHandlerPayload>,\n    NativeViewGestureConfig {}\n\nexport const nativeViewProps = [\n  ...baseGestureHandlerProps,\n  ...nativeViewGestureHandlerProps,\n] as const;\n\nexport const nativeViewHandlerName = 'NativeViewGestureHandler';\n\n/**\n * @deprecated NativeViewGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Native()` instead.\n */\nexport type NativeViewGestureHandler = typeof NativeViewGestureHandler;\n\n/**\n * @deprecated NativeViewGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Native()` instead.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const NativeViewGestureHandler = createHandler<\n  NativeViewGestureHandlerProps,\n  NativeViewGestureHandlerPayload\n>({\n  name: nativeViewHandlerName,\n  allowedProps: nativeViewProps,\n  config: {},\n});\n"]}