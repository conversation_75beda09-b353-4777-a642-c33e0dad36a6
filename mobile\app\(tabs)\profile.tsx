import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React from "react";
import {
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { <PERSON><PERSON>, Card } from "../../src/components/ui";
import {
  BorderRadius,
  FontSizes,
  Spacing,
  TunaWorkColors,
} from "../../src/constants";
import { availableLocales, useLocale, useTranslation } from "../../src/i18n";

export default function ProfileScreen() {
  const { t } = useTranslation();
  const { locale, changeLocale } = useLocale();
  const { resetOnboarding } = useOnboarding();

  // Mock user data - in a real app, this would come from authentication state
  const user = {
    firstName: "Marie",
    lastName: "Kabila",
    email: "<EMAIL>",
    avatar:
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    title: "Développeuse Web Full-Stack",
    isFreelancer: true,
    rating: 4.9,
    reviewCount: 127,
    completedProjects: 89,
    memberSince: "2022-01-15",
  };

  const menuItems = [
    {
      icon: "person-outline",
      title: t("profile.edit"),
      subtitle: "Modifier vos informations personnelles",
      onPress: () => router.push("/profile/edit"),
    },
    {
      icon: "briefcase-outline",
      title: "Mes projets",
      subtitle: "Gérer vos projets en cours et terminés",
      onPress: () => router.push("/profile/projects"),
    },
    {
      icon: "card-outline",
      title: "Paiements",
      subtitle: "Historique et méthodes de paiement",
      onPress: () => router.push("/profile/payments"),
    },
    {
      icon: "notifications-outline",
      title: t("profile.notifications"),
      subtitle: "Gérer vos préférences de notification",
      onPress: () => router.push("/profile/notifications"),
    },
    {
      icon: "shield-outline",
      title: t("profile.privacy"),
      subtitle: "Paramètres de confidentialité",
      onPress: () => router.push("/profile/privacy"),
    },
    {
      icon: "help-circle-outline",
      title: t("profile.help"),
      subtitle: "Centre d'aide et support",
      onPress: () => router.push("/profile/help"),
    },
    {
      icon: "document-text-outline",
      title: t("profile.terms"),
      subtitle: "Conditions d'utilisation",
      onPress: () => router.push("/profile/terms"),
    },
    {
      icon: "refresh-outline",
      title: "Revoir l'introduction",
      subtitle: "Relancer l'onboarding de l'application",
      onPress: resetOnboarding,
    },
  ];

  const handleLanguageChange = (newLocale: "fr" | "ln") => {
    changeLocale(newLocale);
  };

  const handleLogout = () => {
    // In a real app, this would clear authentication state
    router.replace("/auth/login");
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>{t("profile.title")}</Text>
          <TouchableOpacity style={styles.settingsButton}>
            <Ionicons
              name="settings-outline"
              size={24}
              color={TunaWorkColors.secondary[600]}
            />
          </TouchableOpacity>
        </View>

        {/* User Info Card */}
        <Card style={styles.userCard} padding="lg">
          <View style={styles.userHeader}>
            <Image source={{ uri: user.avatar }} style={styles.avatar} />
            <View style={styles.userInfo}>
              <Text style={styles.userName}>
                {user.firstName} {user.lastName}
              </Text>
              <Text style={styles.userTitle}>{user.title}</Text>
              <Text style={styles.userEmail}>{user.email}</Text>
            </View>
            <TouchableOpacity style={styles.editButton}>
              <Ionicons
                name="pencil"
                size={20}
                color={TunaWorkColors.primary[500]}
              />
            </TouchableOpacity>
          </View>

          {user.isFreelancer && (
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <View style={styles.ratingContainer}>
                  <Ionicons name="star" size={16} color="#F59E0B" />
                  <Text style={styles.rating}>{user.rating}</Text>
                </View>
                <Text style={styles.statLabel}>
                  Note ({user.reviewCount} avis)
                </Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{user.completedProjects}</Text>
                <Text style={styles.statLabel}>Projets terminés</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>
                  {new Date(user.memberSince).getFullYear()}
                </Text>
                <Text style={styles.statLabel}>Membre depuis</Text>
              </View>
            </View>
          )}
        </Card>

        {/* Language Selection */}
        <Card style={styles.languageCard} padding="md">
          <Text style={styles.sectionTitle}>Langue / Lokota</Text>
          <View style={styles.languageOptions}>
            {availableLocales.map((lang) => (
              <TouchableOpacity
                key={lang.code}
                style={[
                  styles.languageOption,
                  locale === lang.code && styles.activeLanguageOption,
                ]}
                onPress={() => handleLanguageChange(lang.code as "fr" | "ln")}
              >
                <Text style={styles.languageFlag}>{lang.flag}</Text>
                <Text
                  style={[
                    styles.languageName,
                    locale === lang.code && styles.activeLanguageName,
                  ]}
                >
                  {lang.name}
                </Text>
                {locale === lang.code && (
                  <Ionicons
                    name="checkmark"
                    size={20}
                    color={TunaWorkColors.primary[500]}
                  />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </Card>

        {/* Menu Items */}
        <View style={styles.menuContainer}>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.menuItem}
              onPress={item.onPress}
            >
              <View style={styles.menuItemLeft}>
                <View style={styles.menuIconContainer}>
                  <Ionicons
                    name={item.icon as any}
                    size={24}
                    color={TunaWorkColors.primary[500]}
                  />
                </View>
                <View style={styles.menuItemContent}>
                  <Text style={styles.menuItemTitle}>{item.title}</Text>
                  <Text style={styles.menuItemSubtitle}>{item.subtitle}</Text>
                </View>
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={TunaWorkColors.secondary[400]}
              />
            </TouchableOpacity>
          ))}
        </View>

        {/* Logout Button */}
        <View style={styles.logoutContainer}>
          <Button
            title={t("profile.logout")}
            variant="danger"
            onPress={handleLogout}
            leftIcon={
              <Ionicons name="log-out-outline" size={20} color="#FFFFFF" />
            }
          />
        </View>

        {/* App Version */}
        <Text style={styles.versionText}>TunaWork Mobile v1.0.0</Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: TunaWorkColors.secondary[50],
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    backgroundColor: "#FFFFFF",
  },
  headerTitle: {
    fontSize: FontSizes["2xl"],
    fontWeight: "700",
    color: TunaWorkColors.secondary[900],
  },
  settingsButton: {
    padding: Spacing.sm,
  },
  userCard: {
    margin: Spacing.md,
  },
  userHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Spacing.md,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  userInfo: {
    flex: 1,
    marginLeft: Spacing.md,
  },
  userName: {
    fontSize: FontSizes.xl,
    fontWeight: "700",
    color: TunaWorkColors.secondary[900],
  },
  userTitle: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[600],
    marginTop: 2,
  },
  userEmail: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[500],
    marginTop: 2,
  },
  editButton: {
    padding: Spacing.sm,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: TunaWorkColors.secondary[200],
  },
  statItem: {
    alignItems: "center",
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  rating: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
    marginLeft: 4,
  },
  statValue: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
  },
  statLabel: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.secondary[500],
    textAlign: "center",
    marginTop: 2,
  },
  languageCard: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
    marginBottom: Spacing.sm,
  },
  languageOptions: {
    gap: Spacing.sm,
  },
  languageOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: Spacing.sm,
    borderRadius: BorderRadius.lg,
    backgroundColor: TunaWorkColors.secondary[50],
  },
  activeLanguageOption: {
    backgroundColor: TunaWorkColors.primary[50],
    borderWidth: 1,
    borderColor: TunaWorkColors.primary[200],
  },
  languageFlag: {
    fontSize: FontSizes.lg,
    marginRight: Spacing.sm,
  },
  languageName: {
    flex: 1,
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[700],
  },
  activeLanguageName: {
    color: TunaWorkColors.primary[700],
    fontWeight: "500",
  },
  menuContainer: {
    backgroundColor: "#FFFFFF",
    marginHorizontal: Spacing.md,
    borderRadius: BorderRadius.xl,
    overflow: "hidden",
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: TunaWorkColors.secondary[100],
  },
  menuItemLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  menuIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: TunaWorkColors.primary[50],
    alignItems: "center",
    justifyContent: "center",
    marginRight: Spacing.sm,
  },
  menuItemContent: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: FontSizes.base,
    fontWeight: "500",
    color: TunaWorkColors.secondary[900],
  },
  menuItemSubtitle: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
    marginTop: 2,
  },
  logoutContainer: {
    padding: Spacing.md,
    marginTop: Spacing.lg,
  },
  versionText: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.secondary[400],
    textAlign: "center",
    marginBottom: Spacing.lg,
  },
});
