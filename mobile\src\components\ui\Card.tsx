import React from 'react';
import { View, StyleSheet, ViewStyle, ViewProps } from 'react-native';
import { Colors, TunaWorkColors, BorderRadius, Spacing, Shadows } from '../../constants';

interface CardProps extends ViewProps {
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}

export function Card({
  variant = 'default',
  padding = 'md',
  children,
  style,
  ...props
}: CardProps) {
  const cardStyle: ViewStyle = {
    ...styles.base,
    ...styles[variant],
    ...styles[`padding_${padding}`],
  };

  return (
    <View style={[cardStyle, style]} {...props}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  base: {
    borderRadius: BorderRadius.xl,
    backgroundColor: '#FFFFFF',
  },
  default: {
    ...Shadows.sm,
  },
  elevated: {
    ...Shadows.lg,
  },
  outlined: {
    borderWidth: 1,
    borderColor: TunaWorkColors.secondary[200],
    shadowOpacity: 0,
    elevation: 0,
  },
  filled: {
    backgroundColor: TunaWorkColors.secondary[50],
    shadowOpacity: 0,
    elevation: 0,
  },
  padding_none: {
    padding: 0,
  },
  padding_sm: {
    padding: Spacing.sm,
  },
  padding_md: {
    padding: Spacing.md,
  },
  padding_lg: {
    padding: Spacing.lg,
  },
});
