{"version": 3, "sources": ["WheelEventManager.ts"], "names": ["WheelEventManager", "EventManager", "x", "y", "_event", "wheelDelta", "event", "deltaX", "deltaY", "adaptedEvent", "mapEvent", "onWheel", "registerListeners", "view", "addEventListener", "reset<PERSON><PERSON><PERSON>", "wheelCallback", "unregisterListeners", "removeEventListener", "clientX", "clientY", "offsetX", "offsetY", "pointerId", "eventType", "EventTypes", "MOVE", "pointerType", "PointerType", "OTHER", "time", "timeStamp", "wheelDeltaY", "resetManager"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;;;;;AAEe,MAAMA,iBAAN,SAAgCC,qBAAhC,CAA0D;AAAA;AAAA;;AAAA,wCAClD;AAAEC,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE;AAAX,KADkD;;AAAA,wCAGjDC,MAAD,IAA0B;AAC7C,WAAKC,UAAL,GAAkB;AAAEH,QAAAA,CAAC,EAAE,CAAL;AAAQC,QAAAA,CAAC,EAAE;AAAX,OAAlB;AACD,KALsE;;AAAA,2CAO9CG,KAAD,IAAuB;AAC7C,WAAKD,UAAL,CAAgBH,CAAhB,IAAqBI,KAAK,CAACC,MAA3B;AACA,WAAKF,UAAL,CAAgBF,CAAhB,IAAqBG,KAAK,CAACE,MAA3B;AAEA,YAAMC,YAAY,GAAG,KAAKC,QAAL,CAAcJ,KAAd,CAArB;AACA,WAAKK,OAAL,CAAaF,YAAb;AACD,KAbsE;AAAA;;AAehEG,EAAAA,iBAAiB,GAAS;AAC/B,SAAKC,IAAL,CAAUC,gBAAV,CAA2B,aAA3B,EAA0C,KAAKC,UAA/C;AACA,SAAKF,IAAL,CAAUC,gBAAV,CAA2B,OAA3B,EAAoC,KAAKE,aAAzC;AACD;;AAEMC,EAAAA,mBAAmB,GAAS;AACjC,SAAKJ,IAAL,CAAUK,mBAAV,CAA8B,aAA9B,EAA6C,KAAKH,UAAlD;AACA,SAAKF,IAAL,CAAUK,mBAAV,CAA8B,OAA9B,EAAuC,KAAKF,aAA5C;AACD;;AAESN,EAAAA,QAAQ,CAACJ,KAAD,EAAkC;AAClD,WAAO;AACLJ,MAAAA,CAAC,EAAEI,KAAK,CAACa,OAAN,GAAgB,KAAKd,UAAL,CAAgBH,CAD9B;AAELC,MAAAA,CAAC,EAAEG,KAAK,CAACc,OAAN,GAAgB,KAAKf,UAAL,CAAgBF,CAF9B;AAGLkB,MAAAA,OAAO,EAAEf,KAAK,CAACe,OAAN,GAAgBf,KAAK,CAACC,MAH1B;AAILe,MAAAA,OAAO,EAAEhB,KAAK,CAACgB,OAAN,GAAgBhB,KAAK,CAACE,MAJ1B;AAKLe,MAAAA,SAAS,EAAE,CAAC,CALP;AAMLC,MAAAA,SAAS,EAAEC,uBAAWC,IANjB;AAOLC,MAAAA,WAAW,EAAEC,yBAAYC,KAPpB;AAQLC,MAAAA,IAAI,EAAExB,KAAK,CAACyB,SARP;AASL;AACAC,MAAAA,WAAW,EAAE1B,KAAK,CAAC0B;AAVd,KAAP;AAYD;;AAEMC,EAAAA,YAAY,GAAS;AAC1B,UAAMA,YAAN;AACD;;AA1CsE", "sourcesContent": ["import EventManager from './EventManager';\nimport { AdaptedEvent, EventTypes } from '../interfaces';\nimport { PointerType } from '../../PointerType';\n\nexport default class WheelEventManager extends EventManager<HTMLElement> {\n  private wheelDelta = { x: 0, y: 0 };\n\n  private resetDelta = (_event: PointerEvent) => {\n    this.wheelDelta = { x: 0, y: 0 };\n  };\n\n  private wheelCallback = (event: WheelEvent) => {\n    this.wheelDelta.x += event.deltaX;\n    this.wheelDelta.y += event.deltaY;\n\n    const adaptedEvent = this.mapEvent(event);\n    this.onWheel(adaptedEvent);\n  };\n\n  public registerListeners(): void {\n    this.view.addEventListener('pointermove', this.resetDelta);\n    this.view.addEventListener('wheel', this.wheelCallback);\n  }\n\n  public unregisterListeners(): void {\n    this.view.removeEventListener('pointermove', this.resetDelta);\n    this.view.removeEventListener('wheel', this.wheelCallback);\n  }\n\n  protected mapEvent(event: WheelEvent): AdaptedEvent {\n    return {\n      x: event.clientX + this.wheelDelta.x,\n      y: event.clientY + this.wheelDelta.y,\n      offsetX: event.offsetX - event.deltaX,\n      offsetY: event.offsetY - event.deltaY,\n      pointerId: -1,\n      eventType: EventTypes.MOVE,\n      pointerType: PointerType.OTHER,\n      time: event.timeStamp,\n      // @ts-ignore It does exist, but it's deprecated\n      wheelDeltaY: event.wheelDeltaY,\n    };\n  }\n\n  public resetManager(): void {\n    super.resetManager();\n  }\n}\n"]}