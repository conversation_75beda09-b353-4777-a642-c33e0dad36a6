{"version": 3, "sources": ["gestureStateManager.ts"], "names": ["Reanimated", "State", "tagMessage", "warningMessage", "REANIMATED_AVAILABLE", "useSharedValue", "undefined", "setGestureState", "create", "handlerTag", "begin", "BEGAN", "console", "warn", "activate", "ACTIVE", "fail", "FAILED", "end", "END", "GestureStateManager"], "mappings": "AAAA,SAASA,UAAT,QAA2B,qBAA3B;AACA,SAASC,KAAT,QAAsB,aAAtB;AACA,SAASC,UAAT,QAA2B,aAA3B;AASA,MAAMC,cAAc,GAAGD,UAAU,CAC/B,kFAD+B,CAAjC,C,CAIA;AACA;;AACA,MAAME,oBAAoB,GAAG,CAAAJ,UAAU,SAAV,IAAAA,UAAU,WAAV,YAAAA,UAAU,CAAEK,cAAZ,MAA+BC,SAA5D;AACA,MAAMC,eAAe,GAAGP,UAAH,aAAGA,UAAH,uBAAGA,UAAU,CAAEO,eAApC;;AAEA,SAASC,MAAT,CAAgBC,UAAhB,EAA6D;AAC3D;;AACA,SAAO;AACLC,IAAAA,KAAK,EAAE,MAAM;AACX;;AACA,UAAIN,oBAAJ,EAA0B;AACxB;AACA;AACAG,QAAAA,eAAe,CAAEE,UAAF,EAAcR,KAAK,CAACU,KAApB,CAAf;AACD,OAJD,MAIO;AACLC,QAAAA,OAAO,CAACC,IAAR,CAAaV,cAAb;AACD;AACF,KAVI;AAYLW,IAAAA,QAAQ,EAAE,MAAM;AACd;;AACA,UAAIV,oBAAJ,EAA0B;AACxB;AACA;AACAG,QAAAA,eAAe,CAAEE,UAAF,EAAcR,KAAK,CAACc,MAApB,CAAf;AACD,OAJD,MAIO;AACLH,QAAAA,OAAO,CAACC,IAAR,CAAaV,cAAb;AACD;AACF,KArBI;AAuBLa,IAAAA,IAAI,EAAE,MAAM;AACV;;AACA,UAAIZ,oBAAJ,EAA0B;AACxB;AACA;AACAG,QAAAA,eAAe,CAAEE,UAAF,EAAcR,KAAK,CAACgB,MAApB,CAAf;AACD,OAJD,MAIO;AACLL,QAAAA,OAAO,CAACC,IAAR,CAAaV,cAAb;AACD;AACF,KAhCI;AAkCLe,IAAAA,GAAG,EAAE,MAAM;AACT;;AACA,UAAId,oBAAJ,EAA0B;AACxB;AACA;AACAG,QAAAA,eAAe,CAAEE,UAAF,EAAcR,KAAK,CAACkB,GAApB,CAAf;AACD,OAJD,MAIO;AACLP,QAAAA,OAAO,CAACC,IAAR,CAAaV,cAAb;AACD;AACF;AA3CI,GAAP;AA6CD;;AAED,OAAO,MAAMiB,mBAAmB,GAAG;AACjCZ,EAAAA;AADiC,CAA5B", "sourcesContent": ["import { Reanimated } from './reanimatedWrapper';\nimport { State } from '../../State';\nimport { tagMessage } from '../../utils';\n\nexport interface GestureStateManagerType {\n  begin: () => void;\n  activate: () => void;\n  fail: () => void;\n  end: () => void;\n}\n\nconst warningMessage = tagMessage(\n  'react-native-reanimated is required in order to use synchronous state management'\n);\n\n// Check if reanimated module is available, but look for useSharedValue as conditional\n// require of reanimated can sometimes return content of `utils.ts` file (?)\nconst REANIMATED_AVAILABLE = Reanimated?.useSharedValue !== undefined;\nconst setGestureState = Reanimated?.setGestureState;\n\nfunction create(handlerTag: number): GestureStateManagerType {\n  'worklet';\n  return {\n    begin: () => {\n      'worklet';\n      if (REANIMATED_AVAILABLE) {\n        // When Reanimated is available, setGestureState should be defined\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        setGestureState!(handlerTag, State.BEGAN);\n      } else {\n        console.warn(warningMessage);\n      }\n    },\n\n    activate: () => {\n      'worklet';\n      if (REANIMATED_AVAILABLE) {\n        // When Reanimated is available, setGestureState should be defined\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        setGestureState!(handlerTag, State.ACTIVE);\n      } else {\n        console.warn(warningMessage);\n      }\n    },\n\n    fail: () => {\n      'worklet';\n      if (REANIMATED_AVAILABLE) {\n        // When Reanimated is available, setGestureState should be defined\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        setGestureState!(handlerTag, State.FAILED);\n      } else {\n        console.warn(warningMessage);\n      }\n    },\n\n    end: () => {\n      'worklet';\n      if (REANIMATED_AVAILABLE) {\n        // When Reanimated is available, setGestureState should be defined\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        setGestureState!(handlerTag, State.END);\n      } else {\n        console.warn(warningMessage);\n      }\n    },\n  };\n}\n\nexport const GestureStateManager = {\n  create,\n};\n"]}