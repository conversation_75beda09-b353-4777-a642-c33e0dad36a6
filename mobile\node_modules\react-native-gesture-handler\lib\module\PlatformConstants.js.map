{"version": 3, "sources": ["PlatformConstants.ts"], "names": ["NativeModules", "Platform", "PlatformConstants", "constants"], "mappings": ";;AAAA,SAASA,aAAT,EAAwBC,QAAxB,QAAwC,cAAxC;AAMA,wCAAgBD,aAAhB,aAAgBA,aAAhB,uBAAgBA,aAAa,CAAEE,iBAA/B,yEACED,QAAQ,CAACE,SADX", "sourcesContent": ["import { NativeModules, Platform } from 'react-native';\n\ntype PlatformConstants = {\n  forceTouchAvailable: boolean;\n};\n\nexport default (NativeModules?.PlatformConstants ??\n  Platform.constants) as PlatformConstants;\n"]}