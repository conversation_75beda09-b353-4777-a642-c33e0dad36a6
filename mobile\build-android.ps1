# TunaWork - Script de génération APK Android
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    TunaWork - Génération APK Android" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Vérifier si Node.js est installé
try {
    $nodeVersion = node --version
    Write-Host "✓ Node.js détecté: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Node.js n'est pas installé ou non accessible" -ForegroundColor Red
    Write-Host "Veuillez installer Node.js depuis https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "Appuyez sur Entrée pour continuer..."
    exit 1
}

# Vérifier si npm est installé
try {
    $npmVersion = npm --version
    Write-Host "✓ npm détecté: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ npm n'est pas installé" -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour continuer..."
    exit 1
}

Write-Host ""
Write-Host "Étape 1: Installation d'EAS CLI..." -ForegroundColor Yellow
try {
    npm install -g @expo/eas-cli
    Write-Host "✓ EAS CLI installé avec succès" -ForegroundColor Green
} catch {
    Write-Host "✗ Erreur lors de l'installation d'EAS CLI" -ForegroundColor Red
    Write-Host "Essayez d'exécuter PowerShell en tant qu'administrateur" -ForegroundColor Yellow
    Read-Host "Appuyez sur Entrée pour continuer..."
    exit 1
}

Write-Host ""
Write-Host "Étape 2: Vérification de la connexion Expo..." -ForegroundColor Yellow
Write-Host "Si vous n'êtes pas connecté, vous serez invité à vous connecter" -ForegroundColor Cyan

Write-Host ""
Write-Host "Étape 3: Configuration du projet..." -ForegroundColor Yellow
try {
    eas build:configure
    Write-Host "✓ Projet configuré" -ForegroundColor Green
} catch {
    Write-Host "⚠ Configuration manuelle nécessaire" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Étape 4: Génération de l'APK Android..." -ForegroundColor Yellow
Write-Host "Cela peut prendre plusieurs minutes..." -ForegroundColor Cyan
try {
    eas build --platform android --profile preview
    Write-Host "✓ Build lancé avec succès!" -ForegroundColor Green
} catch {
    Write-Host "✗ Erreur lors du lancement du build" -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour continuer..."
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Génération en cours..." -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "L'APK sera disponible dans votre dashboard Expo une fois terminé" -ForegroundColor Green
Write-Host "Visitez: https://expo.dev/" -ForegroundColor Blue
Write-Host ""
Write-Host "Vous recevrez un email quand le build sera terminé" -ForegroundColor Yellow
Write-Host ""
Read-Host "Appuyez sur Entrée pour continuer..."
