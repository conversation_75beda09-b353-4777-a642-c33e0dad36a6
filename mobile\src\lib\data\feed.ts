export interface FeedPost {
  id: string;
  type: 'project' | 'announcement' | 'tip' | 'success_story';
  title: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar: string;
    title: string;
  };
  timestamp: string;
  likes: number;
  comments: number;
  shares: number;
  isLiked: boolean;
  tags: string[];
  image?: string;
  budget?: {
    min: number;
    max: number;
    currency: 'USD' | 'CDF';
  };
  deadline?: string;
  skills?: string[];
}

export const feedPosts: FeedPost[] = [
  {
    id: '1',
    type: 'project',
    title: 'Développement d\'une application mobile de livraison',
    content: 'Nous recherchons un développeur React Native expérimenté pour créer une application de livraison de nourriture. L\'app doit inclure un système de paiement mobile money et une interface intuitive.',
    author: {
      id: 'client1',
      name: 'Restaurant Chez Mama',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face',
      title: 'Propriétaire de restaurant'
    },
    timestamp: '2024-01-20T10:30:00Z',
    likes: 24,
    comments: 8,
    shares: 3,
    isLiked: false,
    tags: ['React Native', 'Mobile', 'Paiement'],
    budget: {
      min: 800,
      max: 1500,
      currency: 'USD'
    },
    deadline: '2024-02-15',
    skills: ['React Native', 'Node.js', 'MongoDB', 'Payment Integration']
  },
  {
    id: '2',
    type: 'tip',
    title: 'Comment optimiser votre profil freelancer',
    content: '5 conseils pour améliorer votre visibilité sur TunaWork :\n\n1. Utilisez des mots-clés pertinents\n2. Ajoutez des exemples de votre travail\n3. Maintenez un taux de réponse élevé\n4. Collectez des avis positifs\n5. Mettez à jour régulièrement vos compétences',
    author: {
      id: 'admin1',
      name: 'Équipe TunaWork',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face',
      title: 'Équipe TunaWork'
    },
    timestamp: '2024-01-19T14:15:00Z',
    likes: 156,
    comments: 23,
    shares: 45,
    isLiked: true,
    tags: ['Conseils', 'Profil', 'Optimisation'],
    image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=200&fit=crop'
  },
  {
    id: '3',
    type: 'success_story',
    title: 'Succès : 50 projets terminés avec succès !',
    content: 'Je viens de terminer mon 50ème projet sur TunaWork ! Un grand merci à tous mes clients pour leur confiance. Spécialisé en développement web, je suis toujours disponible pour de nouveaux défis.',
    author: {
      id: 'freelancer1',
      name: 'Marie Kabila',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face',
      title: 'Développeuse Full-Stack'
    },
    timestamp: '2024-01-18T16:45:00Z',
    likes: 89,
    comments: 12,
    shares: 6,
    isLiked: false,
    tags: ['Succès', 'Milestone', 'Développement Web']
  },
  {
    id: '4',
    type: 'project',
    title: 'Design d\'identité visuelle pour startup tech',
    content: 'Startup en pleine croissance recherche un designer graphique pour créer son identité visuelle complète : logo, charte graphique, cartes de visite, et supports marketing.',
    author: {
      id: 'client2',
      name: 'TechStart Congo',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face',
      title: 'CEO Startup'
    },
    timestamp: '2024-01-17T09:20:00Z',
    likes: 34,
    comments: 15,
    shares: 8,
    isLiked: true,
    tags: ['Design', 'Identité visuelle', 'Startup'],
    budget: {
      min: 300,
      max: 600,
      currency: 'USD'
    },
    deadline: '2024-02-01',
    skills: ['Adobe Illustrator', 'Photoshop', 'Branding', 'Print Design']
  },
  {
    id: '5',
    type: 'announcement',
    title: 'Nouvelle fonctionnalité : Paiement en CDF',
    content: 'Nous sommes ravis d\'annoncer que TunaWork supporte maintenant les paiements en Francs Congolais (CDF) ! Cette nouvelle option facilite les transactions locales et rend notre plateforme plus accessible.',
    author: {
      id: 'admin1',
      name: 'Équipe TunaWork',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face',
      title: 'Équipe TunaWork'
    },
    timestamp: '2024-01-16T11:00:00Z',
    likes: 203,
    comments: 45,
    shares: 67,
    isLiked: false,
    tags: ['Nouveauté', 'Paiement', 'CDF'],
    image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=200&fit=crop'
  }
];
