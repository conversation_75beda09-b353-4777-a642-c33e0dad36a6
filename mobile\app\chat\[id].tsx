import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useRef, useState } from "react";
import {
  FlatList,
  Image,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import {
  BorderRadius,
  FontSizes,
  Spacing,
  TunaWorkColors,
} from "../../src/constants";

interface Message {
  id: string;
  text: string;
  senderId: string;
  timestamp: string;
  type: "text" | "file" | "image" | "system";
  fileUrl?: string;
  fileName?: string;
  isRead: boolean;
}

const mockMessages: Message[] = [
  {
    id: "1",
    text: "Bonjour ! J'ai vu votre profil et je suis intéressé par vos services de design.",
    senderId: "client",
    timestamp: "10:30",
    type: "text",
    isRead: true,
  },
  {
    id: "2",
    text: "Bonjour ! Merci pour votre message. Je serais ravi de travailler avec vous. Pouvez-vous me parler de votre projet ?",
    senderId: "freelancer",
    timestamp: "10:32",
    type: "text",
    isRead: true,
  },
  {
    id: "3",
    text: "J'ai besoin d'un design pour une application mobile de e-commerce. Voici le brief détaillé.",
    senderId: "client",
    timestamp: "10:35",
    type: "text",
    isRead: true,
  },
  {
    id: "4",
    text: "brief-projet.pdf",
    senderId: "client",
    timestamp: "10:35",
    type: "file",
    fileName: "brief-projet.pdf",
    isRead: true,
  },
  {
    id: "5",
    text: "Parfait ! J'ai bien reçu le brief. Je peux vous proposer un devis de 500$ pour ce projet. Le délai serait de 2 semaines.",
    senderId: "freelancer",
    timestamp: "10:45",
    type: "text",
    isRead: true,
  },
  {
    id: "6",
    text: "Le prix me convient parfaitement ! Quand pouvez-vous commencer ?",
    senderId: "client",
    timestamp: "11:00",
    type: "text",
    isRead: false,
  },
];

export default function ChatScreen() {
  const { id } = useLocalSearchParams();
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState(mockMessages);
  const flatListRef = useRef<FlatList>(null);

  const currentUserId = "freelancer"; // Simulated current user

  const sendMessage = () => {
    if (message.trim()) {
      const newMessage: Message = {
        id: Date.now().toString(),
        text: message.trim(),
        senderId: currentUserId,
        timestamp: new Date().toLocaleTimeString("fr-FR", {
          hour: "2-digit",
          minute: "2-digit",
        }),
        type: "text",
        isRead: false,
      };

      setMessages((prev) => [...prev, newMessage]);
      setMessage("");

      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  };

  const renderMessage = ({ item }: { item: Message }) => {
    const isOwnMessage = item.senderId === currentUserId;

    if (item.type === "system") {
      return (
        <View style={styles.systemMessage}>
          <Text style={styles.systemMessageText}>{item.text}</Text>
        </View>
      );
    }

    return (
      <View
        style={[
          styles.messageContainer,
          isOwnMessage ? styles.ownMessage : styles.otherMessage,
        ]}
      >
        <View
          style={[
            styles.messageBubble,
            isOwnMessage ? styles.ownBubble : styles.otherBubble,
          ]}
        >
          {item.type === "file" && (
            <View style={styles.fileMessage}>
              <Ionicons
                name="document-attach"
                size={24}
                color={isOwnMessage ? "#FFFFFF" : TunaWorkColors.primary[500]}
              />
              <Text
                style={[
                  styles.fileName,
                  {
                    color: isOwnMessage
                      ? "#FFFFFF"
                      : TunaWorkColors.secondary[900],
                  },
                ]}
              >
                {item.fileName}
              </Text>
            </View>
          )}

          {item.type === "text" && (
            <Text
              style={[
                styles.messageText,
                {
                  color: isOwnMessage
                    ? "#FFFFFF"
                    : TunaWorkColors.secondary[900],
                },
              ]}
            >
              {item.text}
            </Text>
          )}

          <View style={styles.messageFooter}>
            <Text
              style={[
                styles.messageTime,
                {
                  color: isOwnMessage
                    ? "rgba(255,255,255,0.7)"
                    : TunaWorkColors.secondary[500],
                },
              ]}
            >
              {item.timestamp}
            </Text>
            {isOwnMessage && (
              <Ionicons
                name={item.isRead ? "checkmark-done" : "checkmark"}
                size={16}
                color={item.isRead ? "#10B981" : "rgba(255,255,255,0.7)"}
              />
            )}
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons
            name="arrow-back"
            size={24}
            color={TunaWorkColors.secondary[900]}
          />
        </TouchableOpacity>

        <View style={styles.headerInfo}>
          <Image
            source={{
              uri: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
            }}
            style={styles.headerAvatar}
          />
          <View style={styles.headerText}>
            <Text style={styles.headerName}>Marie Kabila</Text>
            <Text style={styles.headerStatus}>
              En ligne • Design d'application mobile
            </Text>
          </View>
        </View>

        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons
              name="call"
              size={20}
              color={TunaWorkColors.primary[500]}
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons
              name="videocam"
              size={20}
              color={TunaWorkColors.primary[500]}
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons
              name="ellipsis-vertical"
              size={20}
              color={TunaWorkColors.secondary[600]}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
      />

      {/* Input */}
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.inputContainer}
      >
        <View style={styles.inputRow}>
          <TouchableOpacity style={styles.attachButton}>
            <Ionicons
              name="add"
              size={24}
              color={TunaWorkColors.primary[500]}
            />
          </TouchableOpacity>

          <View style={styles.textInputContainer}>
            <TextInput
              style={styles.textInput}
              placeholder="Tapez votre message..."
              placeholderTextColor={TunaWorkColors.secondary[400]}
              value={message}
              onChangeText={setMessage}
              multiline
              maxLength={1000}
            />
          </View>

          <TouchableOpacity
            style={[
              styles.sendButton,
              message.trim() && styles.sendButtonActive,
            ]}
            onPress={sendMessage}
            disabled={!message.trim()}
          >
            <Ionicons
              name="send"
              size={20}
              color={message.trim() ? "#FFFFFF" : TunaWorkColors.secondary[400]}
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F8FAFC",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: TunaWorkColors.secondary[100],
  },
  backButton: {
    padding: Spacing.sm,
    marginRight: Spacing.sm,
  },
  headerInfo: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  headerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: Spacing.sm,
  },
  headerText: {
    flex: 1,
  },
  headerName: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
  },
  headerStatus: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[500],
    marginTop: 2,
  },
  headerActions: {
    flexDirection: "row",
    gap: Spacing.xs,
  },
  headerButton: {
    padding: Spacing.sm,
    backgroundColor: TunaWorkColors.secondary[50],
    borderRadius: BorderRadius.lg,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: Spacing.md,
  },
  messageContainer: {
    paddingHorizontal: Spacing.md,
    marginVertical: Spacing.xs,
  },
  ownMessage: {
    alignItems: "flex-end",
  },
  otherMessage: {
    alignItems: "flex-start",
  },
  messageBubble: {
    maxWidth: "80%",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.xl,
  },
  ownBubble: {
    backgroundColor: TunaWorkColors.primary[500],
    borderBottomRightRadius: BorderRadius.sm,
  },
  otherBubble: {
    backgroundColor: "#FFFFFF",
    borderBottomLeftRadius: BorderRadius.sm,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  messageText: {
    fontSize: FontSizes.base,
    lineHeight: 20,
  },
  messageFooter: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    marginTop: Spacing.xs,
    gap: 4,
  },
  messageTime: {
    fontSize: FontSizes.xs,
  },
  fileMessage: {
    flexDirection: "row",
    alignItems: "center",
    gap: Spacing.sm,
  },
  fileName: {
    fontSize: FontSizes.base,
    fontWeight: "500",
  },
  systemMessage: {
    alignItems: "center",
    marginVertical: Spacing.md,
  },
  systemMessageText: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[500],
    backgroundColor: TunaWorkColors.secondary[100],
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.lg,
  },
  inputContainer: {
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: TunaWorkColors.secondary[100],
  },
  inputRow: {
    flexDirection: "row",
    alignItems: "flex-end",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    gap: Spacing.sm,
  },
  attachButton: {
    padding: Spacing.sm,
    backgroundColor: TunaWorkColors.secondary[50],
    borderRadius: BorderRadius.lg,
  },
  textInputContainer: {
    flex: 1,
    backgroundColor: TunaWorkColors.secondary[50],
    borderRadius: BorderRadius.xl,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    maxHeight: 100,
  },
  textInput: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[900],
    textAlignVertical: "top",
  },
  sendButton: {
    padding: Spacing.sm,
    backgroundColor: TunaWorkColors.secondary[200],
    borderRadius: BorderRadius.lg,
  },
  sendButtonActive: {
    backgroundColor: TunaWorkColors.primary[500],
  },
});
