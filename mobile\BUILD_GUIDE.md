# 📱 Guide de Génération APK - TunaWork Mobile

Ce guide vous explique comment générer l'APK Android pour l'application TunaWork Mobile.

## 🚀 Méthodes de Génération

### Méthode 1: Script Automatique (Recommandé)

#### Windows PowerShell
```powershell
# Exécuter en tant qu'administrateur
.\build-android.ps1
```

#### Windows Batch
```cmd
build-android.bat
```

### Méthode 2: Commandes Manuelles

#### 1. Installation d'EAS CLI
```bash
npm install -g @expo/eas-cli
```

#### 2. Connexion à Expo
```bash
eas login
```

#### 3. Configuration du projet
```bash
eas build:configure
```

#### 4. Génération de l'APK
```bash
# APK de test
eas build --platform android --profile preview

# APK de production
eas build --platform android --profile production
```

## 📋 Prérequis

### Logiciels Requis
- **Node.js** (version 18+) - [Télécharger](https://nodejs.org/)
- **npm** ou **yarn**
- **Compte Expo** - [Créer un compte](https://expo.dev/)

### Configuration Expo
1. Créez un compte sur [expo.dev](https://expo.dev/)
2. Vérifiez votre email
3. Connectez-vous avec `eas login`

## ⚙️ Configuration du Projet

### Fichier `eas.json`
```json
{
  "cli": {
    "version": ">= 12.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "distribution": "internal",
      "android": {
        "buildType": "apk"
      }
    },
    "production": {
      "android": {
        "buildType": "apk"
      }
    }
  }
}
```

### Fichier `app.json`
```json
{
  "expo": {
    "name": "TunaWork Mobile",
    "slug": "tunawork-mobile",
    "version": "1.0.0",
    "android": {
      "package": "com.tunawork.mobile",
      "adaptiveIcon": {
        "foregroundImage": "./assets/images/adaptive-icon.png",
        "backgroundColor": "#ffffff"
      }
    }
  }
}
```

## 🔧 Profils de Build

### Preview (Test)
- **Usage**: Tests internes, partage avec l'équipe
- **Type**: APK
- **Signature**: Debug
- **Commande**: `eas build --platform android --profile preview`

### Production
- **Usage**: Publication sur Google Play Store
- **Type**: APK ou AAB
- **Signature**: Release
- **Commande**: `eas build --platform android --profile production`

## 📦 Processus de Build

### 1. Préparation
- Vérification des dépendances
- Validation de la configuration
- Upload du code source

### 2. Build Cloud
- Compilation sur les serveurs Expo
- Génération de l'APK
- Tests automatiques

### 3. Téléchargement
- Notification par email
- Téléchargement depuis le dashboard
- QR code pour installation directe

## 🌐 Dashboard Expo

### Accès
- URL: [expo.dev](https://expo.dev/)
- Section: **Projects** → **tunawork-mobile** → **Builds**

### Fonctionnalités
- **Historique des builds**
- **Téléchargement APK**
- **Logs de compilation**
- **QR codes d'installation**
- **Partage avec l'équipe**

## 📱 Installation de l'APK

### Méthode 1: QR Code
1. Ouvrez l'appareil photo Android
2. Scannez le QR code depuis le dashboard
3. Suivez les instructions d'installation

### Méthode 2: Téléchargement Direct
1. Téléchargez l'APK depuis le dashboard
2. Transférez sur votre appareil Android
3. Activez "Sources inconnues" dans les paramètres
4. Installez l'APK

### Méthode 3: Expo Go (Développement)
```bash
expo start
```
Scannez le QR code avec l'app Expo Go

## 🚨 Résolution de Problèmes

### Erreur: "EAS CLI not found"
```bash
npm install -g @expo/eas-cli
# ou
npx @expo/eas-cli@latest
```

### Erreur: "Not logged in"
```bash
eas login
```

### Erreur: "Project not configured"
```bash
eas build:configure
```

### Erreur: "Build failed"
1. Vérifiez les logs dans le dashboard
2. Corrigez les erreurs de code
3. Relancez le build

## 📊 Temps de Build

- **Premier build**: 10-15 minutes
- **Builds suivants**: 5-10 minutes
- **Builds avec cache**: 3-5 minutes

## 💡 Conseils

### Optimisation
- Utilisez le cache EAS pour des builds plus rapides
- Testez localement avant de lancer un build
- Utilisez des profils différents pour dev/prod

### Sécurité
- Ne partagez jamais vos credentials Expo
- Utilisez des variables d'environnement pour les secrets
- Activez l'authentification 2FA sur votre compte Expo

### Performance
- Optimisez les images avant le build
- Supprimez les dépendances inutiles
- Utilisez la minification en production

## 📞 Support

### Documentation Officielle
- [Expo EAS Build](https://docs.expo.dev/build/introduction/)
- [Configuration Android](https://docs.expo.dev/build-reference/android-builds/)

### Communauté
- [Forum Expo](https://forums.expo.dev/)
- [Discord Expo](https://discord.gg/expo)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/expo)

---

**TunaWork Mobile** - Application de services artisanaux congolais 🇨🇩
