{"version": 3, "sources": ["EventManager.ts"], "names": ["EventManager", "constructor", "view", "activePointersCounter", "onPointerDown", "_event", "onPointerAdd", "onPointerUp", "onPointerRemove", "onPointerMove", "onPointerLeave", "onPointerEnter", "onPointerCancel", "onPointerOutOfBounds", "onPointerMoveOver", "onPointerMoveOut", "onWheel", "setOnPointerDown", "callback", "setOnPointerAdd", "setOnPointerUp", "setOnPointerRemove", "setOnPointerMove", "setOnPointerLeave", "setOnPointerEnter", "setOnPointerCancel", "setOnPointerOutOfBounds", "setOnPointerMoveOver", "setOnPointerMoveOut", "setOnWheel", "markAsInBounds", "pointerId", "pointersInBounds", "indexOf", "push", "markAsOutOfBounds", "index", "splice", "resetManager"], "mappings": ";;;;;;;;;AAAA;AAKe,MAAeA,YAAf,CAA+B;AAK5CC,EAAAA,WAAW,CAACC,IAAD,EAAU;AAAA;;AAAA,8CAHkB,EAGlB;;AAAA;;AACnB,SAAKA,IAAL,GAAYA,IAAZ;AACA,SAAKC,qBAAL,GAA6B,CAA7B;AACD;;AAUSC,EAAAA,aAAa,CAACC,MAAD,EAA6B,CAAE;;AAC5CC,EAAAA,YAAY,CAACD,MAAD,EAA6B,CAAE;;AAC3CE,EAAAA,WAAW,CAACF,MAAD,EAA6B,CAAE;;AAC1CG,EAAAA,eAAe,CAACH,MAAD,EAA6B,CAAE;;AAC9CI,EAAAA,aAAa,CAACJ,MAAD,EAA6B,CAAE;;AAC5CK,EAAAA,cAAc,CAACL,MAAD,EAA6B,CAAE,CAvBX,CAuBY;;;AAC9CM,EAAAA,cAAc,CAACN,MAAD,EAA6B,CAAE,CAxBX,CAwBY;;;AAC9CO,EAAAA,eAAe,CAACP,MAAD,EAA6B,CACpD;AACA;AACA;AACA;AACD;;AACSQ,EAAAA,oBAAoB,CAACR,MAAD,EAA6B,CAAE;;AACnDS,EAAAA,iBAAiB,CAACT,MAAD,EAA6B,CAAE;;AAChDU,EAAAA,gBAAgB,CAACV,MAAD,EAA6B,CAAE;;AAC/CW,EAAAA,OAAO,CAACX,MAAD,EAA6B,CAAE;;AAEzCY,EAAAA,gBAAgB,CAACC,QAAD,EAAuC;AAC5D,SAAKd,aAAL,GAAqBc,QAArB;AACD;;AACMC,EAAAA,eAAe,CAACD,QAAD,EAAuC;AAC3D,SAAKZ,YAAL,GAAoBY,QAApB;AACD;;AACME,EAAAA,cAAc,CAACF,QAAD,EAAuC;AAC1D,SAAKX,WAAL,GAAmBW,QAAnB;AACD;;AACMG,EAAAA,kBAAkB,CAACH,QAAD,EAAuC;AAC9D,SAAKV,eAAL,GAAuBU,QAAvB;AACD;;AACMI,EAAAA,gBAAgB,CAACJ,QAAD,EAAuC;AAC5D,SAAKT,aAAL,GAAqBS,QAArB;AACD;;AACMK,EAAAA,iBAAiB,CAACL,QAAD,EAAuC;AAC7D,SAAKR,cAAL,GAAsBQ,QAAtB;AACD;;AACMM,EAAAA,iBAAiB,CAACN,QAAD,EAAuC;AAC7D,SAAKP,cAAL,GAAsBO,QAAtB;AACD;;AACMO,EAAAA,kBAAkB,CAACP,QAAD,EAAuC;AAC9D,SAAKN,eAAL,GAAuBM,QAAvB;AACD;;AACMQ,EAAAA,uBAAuB,CAACR,QAAD,EAAuC;AACnE,SAAKL,oBAAL,GAA4BK,QAA5B;AACD;;AACMS,EAAAA,oBAAoB,CAACT,QAAD,EAAuC;AAChE,SAAKJ,iBAAL,GAAyBI,QAAzB;AACD;;AACMU,EAAAA,mBAAmB,CAACV,QAAD,EAAuC;AAC/D,SAAKH,gBAAL,GAAwBG,QAAxB;AACD;;AACMW,EAAAA,UAAU,CAACX,QAAD,EAAuC;AACtD,SAAKF,OAAL,GAAeE,QAAf;AACD;;AAESY,EAAAA,cAAc,CAACC,SAAD,EAA0B;AAChD,QAAI,KAAKC,gBAAL,CAAsBC,OAAtB,CAA8BF,SAA9B,KAA4C,CAAhD,EAAmD;AACjD;AACD;;AAED,SAAKC,gBAAL,CAAsBE,IAAtB,CAA2BH,SAA3B;AACD;;AAESI,EAAAA,iBAAiB,CAACJ,SAAD,EAA0B;AACnD,UAAMK,KAAa,GAAG,KAAKJ,gBAAL,CAAsBC,OAAtB,CAA8BF,SAA9B,CAAtB;;AAEA,QAAIK,KAAK,GAAG,CAAZ,EAAe;AACb;AACD;;AAED,SAAKJ,gBAAL,CAAsBK,MAAtB,CAA6BD,KAA7B,EAAoC,CAApC;AACD;;AAEME,EAAAA,YAAY,GAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AAEA,SAAKnC,qBAAL,GAA6B,CAA7B;AACA,SAAK6B,gBAAL,GAAwB,EAAxB;AACD;;AArG2C", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-empty-function */\nimport { AdaptedEvent, EventTypes } from '../interfaces';\n\ntype PointerEventCallback = (event: AdaptedEvent) => void;\n\nexport default abstract class EventManager<T> {\n  protected readonly view: T;\n  protected pointersInBounds: number[] = [];\n  protected activePointersCounter: number;\n\n  constructor(view: T) {\n    this.view = view;\n    this.activePointersCounter = 0;\n  }\n\n  public abstract registerListeners(): void;\n  public abstract unregisterListeners(): void;\n\n  protected abstract mapEvent(\n    event: Event,\n    eventType: EventTypes\n  ): AdaptedEvent;\n\n  protected onPointerDown(_event: AdaptedEvent): void {}\n  protected onPointerAdd(_event: AdaptedEvent): void {}\n  protected onPointerUp(_event: AdaptedEvent): void {}\n  protected onPointerRemove(_event: AdaptedEvent): void {}\n  protected onPointerMove(_event: AdaptedEvent): void {}\n  protected onPointerLeave(_event: AdaptedEvent): void {} // Called only when pointer is pressed (or touching)\n  protected onPointerEnter(_event: AdaptedEvent): void {} // Called only when pointer is pressed (or touching)\n  protected onPointerCancel(_event: AdaptedEvent): void {\n    // When pointer cancel is triggered and there are more pointers on the view, only one pointer is cancelled\n    // Because we want all pointers to be cancelled by that event, we are doing it manually by reseting handler and changing activePointersCounter to 0\n    // Events that correspond to removing the pointer (pointerup, touchend) have condition, that they don't perform any action when activePointersCounter\n    // is equal to 0. This prevents counter from going to negative values, when pointers are removed from view after one of them has been cancelled\n  }\n  protected onPointerOutOfBounds(_event: AdaptedEvent): void {}\n  protected onPointerMoveOver(_event: AdaptedEvent): void {}\n  protected onPointerMoveOut(_event: AdaptedEvent): void {}\n  protected onWheel(_event: AdaptedEvent): void {}\n\n  public setOnPointerDown(callback: PointerEventCallback): void {\n    this.onPointerDown = callback;\n  }\n  public setOnPointerAdd(callback: PointerEventCallback): void {\n    this.onPointerAdd = callback;\n  }\n  public setOnPointerUp(callback: PointerEventCallback): void {\n    this.onPointerUp = callback;\n  }\n  public setOnPointerRemove(callback: PointerEventCallback): void {\n    this.onPointerRemove = callback;\n  }\n  public setOnPointerMove(callback: PointerEventCallback): void {\n    this.onPointerMove = callback;\n  }\n  public setOnPointerLeave(callback: PointerEventCallback): void {\n    this.onPointerLeave = callback;\n  }\n  public setOnPointerEnter(callback: PointerEventCallback): void {\n    this.onPointerEnter = callback;\n  }\n  public setOnPointerCancel(callback: PointerEventCallback): void {\n    this.onPointerCancel = callback;\n  }\n  public setOnPointerOutOfBounds(callback: PointerEventCallback): void {\n    this.onPointerOutOfBounds = callback;\n  }\n  public setOnPointerMoveOver(callback: PointerEventCallback): void {\n    this.onPointerMoveOver = callback;\n  }\n  public setOnPointerMoveOut(callback: PointerEventCallback): void {\n    this.onPointerMoveOut = callback;\n  }\n  public setOnWheel(callback: PointerEventCallback): void {\n    this.onWheel = callback;\n  }\n\n  protected markAsInBounds(pointerId: number): void {\n    if (this.pointersInBounds.indexOf(pointerId) >= 0) {\n      return;\n    }\n\n    this.pointersInBounds.push(pointerId);\n  }\n\n  protected markAsOutOfBounds(pointerId: number): void {\n    const index: number = this.pointersInBounds.indexOf(pointerId);\n\n    if (index < 0) {\n      return;\n    }\n\n    this.pointersInBounds.splice(index, 1);\n  }\n\n  public resetManager(): void {\n    // Reseting activePointersCounter is necessary to make gestures such as pinch work properly\n    // There are gestures that end when there is still one active pointer (like pinch/rotation)\n    // When these gestures end, they are reset, but they still receive events from pointer that is active\n    // This causes trouble, since only onPointerDown registers gesture in orchestrator, and while gestures receive\n    // Events from active pointer after they finished, next pointerdown event will be registered as additional pointer, not the first one\n    // This casues trouble like gestures getting stuck in END state, even though they should have gone to UNDETERMINED\n\n    this.activePointersCounter = 0;\n    this.pointersInBounds = [];\n  }\n}\n"]}