{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "React", "_extractLengthList", "_interopRequireDefault", "_util", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "fontRegExp", "fontFamilyPrefix", "fontFamilySuffix", "commaReg", "cachedFontObjectsFromString", "extractSingleFontFamily", "fontFamilyString", "split", "replace", "parseFontString", "font", "prototype", "match", "exec", "isBold", "isItalic", "fontSize", "fontWeight", "fontStyle", "fontFamily", "extractFont", "props", "fontData", "fontVariant", "fontStretch", "textAnchor", "textDecoration", "letterSpacing", "wordSpacing", "kerning", "fontFeatureSettings", "fontVariantLigatures", "fontVariationSettings", "ownedFont", "pickNotNil", "baseFont", "TSpan", "setTSpan", "TSpanImplementation", "<PERSON><PERSON><PERSON><PERSON>", "child", "createElement", "String", "extractText", "container", "x", "y", "dx", "dy", "rotate", "children", "inlineSize", "baselineShift", "verticalAlign", "alignmentBaseline", "textChil<PERSON>n", "Children", "count", "Array", "isArray", "map", "content", "extractLengthList"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractText.tsx"], "mappings": ";;;;;;;;AACA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA+B,IAAAC,KAAA,GAAAH,MAAA;AAE/B,IAAAI,kBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AAAqC,SAAAG,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAGrC,MAAMW,UAAU,GACd,mHAAmH;AACrH,MAAMC,gBAAgB,GAAG,UAAU;AACnC,MAAMC,gBAAgB,GAAG,UAAU;AACnC,MAAMC,QAAQ,GAAG,UAAU;AAE3B,MAAMC,2BAOL,GAAG,CAAC,CAAC;AAEN,SAASC,uBAAuBA,CAACC,gBAAyB,EAAE;EAC1D;EACA;EACA;EACA,OAAOA,gBAAgB,GACnBA,gBAAgB,CACbC,KAAK,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAAC,CAClBK,OAAO,CAACP,gBAAgB,EAAE,EAAE,CAAC,CAC7BO,OAAO,CAACN,gBAAgB,EAAE,EAAE,CAAC,GAChC,IAAI;AACV;AAEA,SAASO,eAAeA,CAACC,IAAY,EAAE;EACrC,IAAIlB,MAAM,CAACmB,SAAS,CAACf,cAAc,CAACC,IAAI,CAACO,2BAA2B,EAAEM,IAAI,CAAC,EAAE;IAC3E,OAAON,2BAA2B,CAACM,IAAI,CAAC;EAC1C;EACA,MAAME,KAAK,GAAGZ,UAAU,CAACa,IAAI,CAACH,IAAI,CAAC;EACnC,IAAI,CAACE,KAAK,EAAE;IACVR,2BAA2B,CAACM,IAAI,CAAC,GAAG,IAAI;IACxC,OAAO,IAAI;EACb;EACA,MAAMI,MAAM,GAAG,MAAM,CAACD,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;EACpC,MAAMG,QAAQ,GAAG,QAAQ,CAACF,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;EACxCR,2BAA2B,CAACM,IAAI,CAAC,GAAG;IAClCM,QAAQ,EAAEJ,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;IACxBK,UAAU,EAAEH,MAAM,GAAG,MAAM,GAAG,QAAQ;IACtCI,SAAS,EAAEH,QAAQ,GAAG,QAAQ,GAAG,QAAQ;IACzCI,UAAU,EAAEd,uBAAuB,CAACO,KAAK,CAAC,CAAC,CAAC;EAC9C,CAAC;EACD,OAAOR,2BAA2B,CAACM,IAAI,CAAC;AAC1C;AAqBO,SAASU,WAAWA,CAACC,KAAgB,EAAE;EAC5C,MAAM;IACJC,QAAQ;IACRJ,SAAS;IACTK,WAAW;IACXN,UAAU;IACVO,WAAW;IACXR,QAAQ;IACRG,UAAU;IACVM,UAAU;IACVC,cAAc;IACdC,aAAa;IACbC,WAAW;IACXC,OAAO;IACPC,mBAAmB;IACnBC,oBAAoB;IACpBC,qBAAqB;IACrBtB;EACF,CAAC,GAAGW,KAAK;EAET,MAAMY,SAAS,GAAG,IAAAC,gBAAU,EAAC;IAC3BZ,QAAQ;IACRJ,SAAS;IACTK,WAAW;IACXN,UAAU;IACVO,WAAW;IACXR,QAAQ;IACRG,UAAU,EAAEd,uBAAuB,CAACc,UAAU,CAAC;IAC/CM,UAAU;IACVC,cAAc;IACdC,aAAa;IACbC,WAAW;IACXC,OAAO;IACPC,mBAAmB;IACnBC,oBAAoB;IACpBC;EACF,CAAC,CAAC;EAEF,MAAMG,QAAQ,GAAG,OAAOzB,IAAI,KAAK,QAAQ,GAAGD,eAAe,CAACC,IAAI,CAAC,GAAGA,IAAI;EAExE,OAAO;IAAE,GAAGyB,QAAQ;IAAE,GAAGF;EAAU,CAAC;AACtC;AAEA,IAAIG,KAA6C;AAE1C,SAASC,QAAQA,CAACC,mBAAkC,EAAE;EAC3DF,KAAK,GAAGE,mBAAmB;AAC7B;AAMA,SAASC,QAAQA,CAACC,KAAgB,EAAE;EAClC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC1D,oBAAOhE,KAAA,CAAAiE,aAAA,CAACL,KAAK,QAAEM,MAAM,CAACF,KAAK,CAAS,CAAC;EACvC,CAAC,MAAM;IACL,OAAOA,KAAK;EACd;AACF;AAee,SAASG,WAAWA,CAACtB,KAAgB,EAAEuB,SAAkB,EAAE;EACxE,MAAM;IACJC,CAAC;IACDC,CAAC;IACDC,EAAE;IACFC,EAAE;IACFC,MAAM;IACNC,QAAQ;IACRC,UAAU;IACVC,aAAa;IACbC,aAAa;IACbC;EACF,CAAC,GAAGjC,KAAK;EAET,MAAMkC,YAAY,GAChB,OAAOL,QAAQ,KAAK,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,GAC1DN,SAAS,gBACPpE,KAAA,CAAAiE,aAAA,CAACL,KAAK,QAAEM,MAAM,CAACQ,QAAQ,CAAS,CAAC,GAC/B,IAAI,GACNM,eAAQ,CAACC,KAAK,CAACP,QAAQ,CAAC,GAAG,CAAC,IAAIQ,KAAK,CAACC,OAAO,CAACT,QAAQ,CAAC,GACzDM,eAAQ,CAACI,GAAG,CAACV,QAAQ,EAAEX,QAAQ,CAAC,GAEhCW,QACD;EAEH,OAAO;IACLW,OAAO,EAAEN,YAAY,KAAK,IAAI,GAAGb,MAAM,CAACQ,QAAQ,CAAC,GAAG,IAAI;IACxDA,QAAQ,EAAEK,YAAY;IACtBJ,UAAU;IACVC,aAAa;IACbC,aAAa;IACbC,iBAAiB;IACjB5C,IAAI,EAAEU,WAAW,CAACC,KAAK,CAAC;IACxBwB,CAAC,EAAE,IAAAiB,0BAAiB,EAACjB,CAAC,CAAC;IACvBC,CAAC,EAAE,IAAAgB,0BAAiB,EAAChB,CAAC,CAAC;IACvBC,EAAE,EAAE,IAAAe,0BAAiB,EAACf,EAAE,CAAC;IACzBC,EAAE,EAAE,IAAAc,0BAAiB,EAACd,EAAE,CAAC;IACzBC,MAAM,EAAE,IAAAa,0BAAiB,EAACb,MAAM;EAClC,CAAC;AACH", "ignoreList": []}