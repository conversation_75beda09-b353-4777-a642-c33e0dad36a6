import { Ionicons } from "@expo/vector-icons";
import React from "react";
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Animated,
} from "react-native";

import {
  BorderRadius,
  FontSizes,
  Spacing,
  TunaWorkColors,
} from "../../constants";

interface MessageBubbleProps {
  message: string;
  isOwnMessage: boolean;
  timestamp: string;
  isRead?: boolean;
  type?: 'text' | 'file' | 'image' | 'system';
  fileName?: string;
  onLongPress?: () => void;
  onPress?: () => void;
}

export function MessageBubble({
  message,
  isOwnMessage,
  timestamp,
  isRead = false,
  type = 'text',
  fileName,
  onLongPress,
  onPress,
}: MessageBubbleProps) {
  const animatedValue = new Animated.Value(0);

  const handlePress = () => {
    Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
    
    onPress?.();
  };

  const scale = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 0.95],
  });

  if (type === 'system') {
    return (
      <View style={styles.systemMessage}>
        <Text style={styles.systemMessageText}>{message}</Text>
      </View>
    );
  }

  return (
    <View style={[
      styles.messageContainer,
      isOwnMessage ? styles.ownMessage : styles.otherMessage
    ]}>
      <Animated.View style={{ transform: [{ scale }] }}>
        <TouchableOpacity
          style={[
            styles.messageBubble,
            isOwnMessage ? styles.ownBubble : styles.otherBubble
          ]}
          onPress={handlePress}
          onLongPress={onLongPress}
          activeOpacity={0.8}
        >
          {type === 'file' && (
            <View style={styles.fileMessage}>
              <View style={[
                styles.fileIcon,
                { backgroundColor: isOwnMessage ? "rgba(255,255,255,0.2)" : TunaWorkColors.primary[50] }
              ]}>
                <Ionicons 
                  name="document-attach" 
                  size={20} 
                  color={isOwnMessage ? "#FFFFFF" : TunaWorkColors.primary[500]} 
                />
              </View>
              <View style={styles.fileInfo}>
                <Text style={[
                  styles.fileName,
                  { color: isOwnMessage ? "#FFFFFF" : TunaWorkColors.secondary[900] }
                ]}>
                  {fileName || "Document"}
                </Text>
                <Text style={[
                  styles.fileSize,
                  { color: isOwnMessage ? "rgba(255,255,255,0.7)" : TunaWorkColors.secondary[500] }
                ]}>
                  PDF • 2.4 MB
                </Text>
              </View>
            </View>
          )}
          
          {type === 'image' && (
            <View style={styles.imageMessage}>
              <View style={[
                styles.imageIcon,
                { backgroundColor: isOwnMessage ? "rgba(255,255,255,0.2)" : TunaWorkColors.primary[50] }
              ]}>
                <Ionicons 
                  name="image" 
                  size={20} 
                  color={isOwnMessage ? "#FFFFFF" : TunaWorkColors.primary[500]} 
                />
              </View>
              <Text style={[
                styles.imageText,
                { color: isOwnMessage ? "#FFFFFF" : TunaWorkColors.secondary[900] }
              ]}>
                Photo envoyée
              </Text>
            </View>
          )}
          
          {type === 'text' && (
            <Text style={[
              styles.messageText,
              { color: isOwnMessage ? "#FFFFFF" : TunaWorkColors.secondary[900] }
            ]}>
              {message}
            </Text>
          )}
          
          <View style={styles.messageFooter}>
            <Text style={[
              styles.messageTime,
              { color: isOwnMessage ? "rgba(255,255,255,0.7)" : TunaWorkColors.secondary[500] }
            ]}>
              {timestamp}
            </Text>
            {isOwnMessage && (
              <View style={styles.readStatus}>
                <Ionicons 
                  name={isRead ? "checkmark-done" : "checkmark"} 
                  size={14} 
                  color={isRead ? "#10B981" : "rgba(255,255,255,0.7)"} 
                />
              </View>
            )}
          </View>
        </TouchableOpacity>
      </Animated.View>
      
      {/* Message tail */}
      <View style={[
        styles.messageTail,
        isOwnMessage ? styles.ownTail : styles.otherTail
      ]} />
    </View>
  );
}

const styles = StyleSheet.create({
  messageContainer: {
    paddingHorizontal: Spacing.md,
    marginVertical: Spacing.xs,
    position: "relative",
  },
  ownMessage: {
    alignItems: "flex-end",
  },
  otherMessage: {
    alignItems: "flex-start",
  },
  messageBubble: {
    maxWidth: "80%",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.xl,
    position: "relative",
  },
  ownBubble: {
    backgroundColor: TunaWorkColors.primary[500],
    borderBottomRightRadius: BorderRadius.sm,
  },
  otherBubble: {
    backgroundColor: "#FFFFFF",
    borderBottomLeftRadius: BorderRadius.sm,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  messageText: {
    fontSize: FontSizes.base,
    lineHeight: 22,
  },
  messageFooter: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    marginTop: Spacing.xs,
    gap: 4,
  },
  messageTime: {
    fontSize: FontSizes.xs,
  },
  readStatus: {
    marginLeft: 2,
  },
  fileMessage: {
    flexDirection: "row",
    alignItems: "center",
    gap: Spacing.sm,
  },
  fileIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    justifyContent: "center",
    alignItems: "center",
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: FontSizes.base,
    fontWeight: "600",
  },
  fileSize: {
    fontSize: FontSizes.xs,
    marginTop: 2,
  },
  imageMessage: {
    flexDirection: "row",
    alignItems: "center",
    gap: Spacing.sm,
  },
  imageIcon: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.md,
    justifyContent: "center",
    alignItems: "center",
  },
  imageText: {
    fontSize: FontSizes.base,
    fontWeight: "500",
  },
  systemMessage: {
    alignItems: "center",
    marginVertical: Spacing.md,
  },
  systemMessageText: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[500],
    backgroundColor: TunaWorkColors.secondary[100],
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.lg,
  },
  messageTail: {
    position: "absolute",
    bottom: 8,
    width: 0,
    height: 0,
  },
  ownTail: {
    right: Spacing.md - 2,
    borderLeftWidth: 8,
    borderLeftColor: TunaWorkColors.primary[500],
    borderTopWidth: 8,
    borderTopColor: "transparent",
  },
  otherTail: {
    left: Spacing.md - 2,
    borderRightWidth: 8,
    borderRightColor: "#FFFFFF",
    borderTopWidth: 8,
    borderTopColor: "transparent",
  },
});
