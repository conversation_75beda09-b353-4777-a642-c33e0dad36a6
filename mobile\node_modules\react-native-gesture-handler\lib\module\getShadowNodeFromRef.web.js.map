{"version": 3, "sources": ["getShadowNodeFromRef.web.ts"], "names": ["getShadowNodeFromRef", "_ref"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,oBAAT,CAA8BC,IAA9B,EAAyC;AAC9C,SAAO,IAAP;AACD", "sourcesContent": ["// Used by GestureDetector (unsupported on web at the moment) to check whether the\n// attached view may get flattened on Fabric. Original implementation causes errors\n// on web due to the static resolution of `require` statements by webpack breaking\n// the conditional importing.\nexport function getShadowNodeFromRef(_ref: any) {\n  return null;\n}\n"]}