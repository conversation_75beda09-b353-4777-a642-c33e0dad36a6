{"version": 3, "sources": ["constants.ts"], "names": ["CONTENT_TOUCHES_DELAY", "CONTENT_TOUCHES_QUICK_TAP_END_DELAY", "MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD", "MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD", "DEG_RAD", "Math", "PI", "EventMap", "Hammer", "INPUT_START", "State", "BEGAN", "INPUT_MOVE", "ACTIVE", "INPUT_END", "END", "INPUT_CANCEL", "FAILED", "Direction", "RIGHT", "LEFT", "UP", "DOWN", "DirectionMap", "DIRECTION_RIGHT", "DIRECTION_LEFT", "DIRECTION_UP", "DIRECTION_DOWN", "HammerInputNames", "HammerDirectionNames", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "DIRECTION_NONE", "DIRECTION_ALL"], "mappings": ";;;;;;;AAAA;;AAEA;;;;AAEO,MAAMA,qBAAqB,GAAG,GAA9B;;AACA,MAAMC,mCAAmC,GAAG,EAA5C;;AACA,MAAMC,oCAAoC,GAAG,GAA7C;;AACA,MAAMC,uCAAuC,GAAG,CAAhD;;AACA,MAAMC,OAAO,GAAGC,IAAI,CAACC,EAAL,GAAU,GAA1B,C,CAEP;;;AACO,MAAMC,QAAQ,GAAG;AACtB,GAACC,kBAAOC,WAAR,GAAsBC,aAAMC,KADN;AAEtB,GAACH,kBAAOI,UAAR,GAAqBF,aAAMG,MAFL;AAGtB,GAACL,kBAAOM,SAAR,GAAoBJ,aAAMK,GAHJ;AAItB,GAACP,kBAAOQ,YAAR,GAAuBN,aAAMO;AAJP,CAAjB;;AAOA,MAAMC,SAAS,GAAG;AACvBC,EAAAA,KAAK,EAAE,CADgB;AAEvBC,EAAAA,IAAI,EAAE,CAFiB;AAGvBC,EAAAA,EAAE,EAAE,CAHmB;AAIvBC,EAAAA,IAAI,EAAE;AAJiB,CAAlB;;AAOA,MAAMC,YAAY,GAAG;AAC1B,GAACf,kBAAOgB,eAAR,GAA0BN,SAAS,CAACC,KADV;AAE1B,GAACX,kBAAOiB,cAAR,GAAyBP,SAAS,CAACE,IAFT;AAG1B,GAACZ,kBAAOkB,YAAR,GAAuBR,SAAS,CAACG,EAHP;AAI1B,GAACb,kBAAOmB,cAAR,GAAyBT,SAAS,CAACI;AAJT,CAArB;;AAOA,MAAMM,gBAAgB,GAAG;AAC9B,GAACpB,kBAAOC,WAAR,GAAsB,OADQ;AAE9B,GAACD,kBAAOI,UAAR,GAAqB,MAFS;AAG9B,GAACJ,kBAAOM,SAAR,GAAoB,KAHU;AAI9B,GAACN,kBAAOQ,YAAR,GAAuB;AAJO,CAAzB;;AAMA,MAAMa,oBAAoB,GAAG;AAClC,GAACrB,kBAAOsB,oBAAR,GAA+B,YADG;AAElC,GAACtB,kBAAOkB,YAAR,GAAuB,IAFW;AAGlC,GAAClB,kBAAOmB,cAAR,GAAyB,MAHS;AAIlC,GAACnB,kBAAOuB,kBAAR,GAA6B,UAJK;AAKlC,GAACvB,kBAAOwB,cAAR,GAAyB,MALS;AAMlC,GAACxB,kBAAOyB,aAAR,GAAwB,KANU;AAOlC,GAACzB,kBAAOgB,eAAR,GAA0B,OAPQ;AAQlC,GAAChB,kBAAOiB,cAAR,GAAyB;AARS,CAA7B", "sourcesContent": ["import Hammer from '@egjs/hammerjs';\n\nimport { State } from '../State';\n\nexport const CONTENT_TOUCHES_DELAY = 240;\nexport const CONTENT_TOUCHES_QUICK_TAP_END_DELAY = 50;\nexport const MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD = 0.1;\nexport const MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD = 7;\nexport const DEG_RAD = Math.PI / 180;\n\n// Map Hammer values to RNGH\nexport const EventMap = {\n  [Hammer.INPUT_START]: State.BEGAN,\n  [Hammer.INPUT_MOVE]: State.ACTIVE,\n  [Hammer.INPUT_END]: State.END,\n  [Hammer.INPUT_CANCEL]: State.FAILED,\n} as const;\n\nexport const Direction = {\n  RIGHT: 1,\n  LEFT: 2,\n  UP: 4,\n  DOWN: 8,\n};\n\nexport const DirectionMap = {\n  [Hammer.DIRECTION_RIGHT]: Direction.RIGHT,\n  [Hammer.DIRECTION_LEFT]: Direction.LEFT,\n  [Hammer.DIRECTION_UP]: Direction.UP,\n  [Hammer.DIRECTION_DOWN]: Direction.DOWN,\n};\n\nexport const HammerInputNames = {\n  [Hammer.INPUT_START]: 'START',\n  [Hammer.INPUT_MOVE]: 'MOVE',\n  [Hammer.INPUT_END]: 'END',\n  [Hammer.INPUT_CANCEL]: 'CANCEL',\n};\nexport const HammerDirectionNames = {\n  [Hammer.DIRECTION_HORIZONTAL]: 'HORIZONTAL',\n  [Hammer.DIRECTION_UP]: 'UP',\n  [Hammer.DIRECTION_DOWN]: 'DOWN',\n  [Hammer.DIRECTION_VERTICAL]: 'VERTICAL',\n  [Hammer.DIRECTION_NONE]: 'NONE',\n  [Hammer.DIRECTION_ALL]: 'ALL',\n  [Hammer.DIRECTION_RIGHT]: 'RIGHT',\n  [Hammer.DIRECTION_LEFT]: 'LEFT',\n};\n"]}