import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React from "react";
import {
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { Button, Card } from "../../src/components/ui";
import {
  BorderRadius,
  FontSizes,
  Spacing,
  TunaWorkColors,
} from "../../src/constants";
import { useTranslation } from "../../src/i18n";
import { featuredFreelancers } from "../../src/lib/data";

export default function HomeScreen() {
  const { t } = useTranslation();

  const stats = [
    { label: t("home.stats.projects"), value: "2,500+", icon: "briefcase" },
    { label: t("home.stats.freelancers"), value: "1,200+", icon: "people" },
    { label: t("home.stats.satisfaction"), value: "98%", icon: "star" },
    { label: t("home.stats.support"), value: "24/7", icon: "headset" },
  ];

  const renderFreelancer = ({
    item,
  }: {
    item: (typeof featuredFreelancers)[0];
  }) => (
    <TouchableOpacity
      style={styles.freelancerCard}
      onPress={() => router.push(`/freelancer/${item.id}` as any)}
    >
      <Card padding="md">
        <View style={styles.freelancerHeader}>
          <Image source={{ uri: item.avatar }} style={styles.avatar} />
          <View style={styles.onlineIndicator}>
            <View
              style={[
                styles.onlineDot,
                { backgroundColor: item.isOnline ? "#10B981" : "#6B7280" },
              ]}
            />
          </View>
        </View>
        <Text style={styles.freelancerName}>
          {item.firstName} {item.lastName}
        </Text>
        <Text style={styles.freelancerTitle}>{item.title}</Text>
        <View style={styles.ratingContainer}>
          <Ionicons name="star" size={16} color="#F59E0B" />
          <Text style={styles.rating}>{item.rating}</Text>
          <Text style={styles.reviewCount}>({item.reviewCount})</Text>
        </View>
        <Text style={styles.hourlyRate}>${item.hourlyRate}/h</Text>
      </Card>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.welcomeText}>{t("home.welcome")}</Text>
            <Text style={styles.subtitle}>{t("home.subtitle")}</Text>
          </View>
          <TouchableOpacity style={styles.notificationButton}>
            <Ionicons
              name="notifications-outline"
              size={24}
              color={TunaWorkColors.secondary[600]}
            />
          </TouchableOpacity>
        </View>

        {/* Hero Section */}
        <LinearGradient
          colors={TunaWorkColors.gradient.primary}
          style={styles.heroSection}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <Text style={styles.heroTitle}>Trouvez le freelancer parfait</Text>
          <Text style={styles.heroSubtitle}>
            Connectez-vous avec les meilleurs talents congolais
          </Text>
          <View style={styles.heroButtons}>
            <Button
              title={t("home.findFreelancers")}
              variant="secondary"
              onPress={() => router.push("/(tabs)/professionals")}
              style={styles.heroButton}
            />
            <Button
              title={t("home.postProject")}
              variant="outline"
              onPress={() => router.push("/auth/login")}
              style={[styles.heroButton, styles.outlineButton]}
            />
          </View>
        </LinearGradient>

        {/* Stats Section */}
        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Nos statistiques</Text>
          <View style={styles.statsGrid}>
            {stats.map((stat, index) => (
              <Card key={index} style={styles.statCard} padding="md">
                <Ionicons
                  name={stat.icon as any}
                  size={24}
                  color={TunaWorkColors.primary[500]}
                  style={styles.statIcon}
                />
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </Card>
            ))}
          </View>
        </View>

        {/* Featured Freelancers */}
        <View style={styles.featuredSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {t("home.featuredFreelancers")}
            </Text>
            <TouchableOpacity
              onPress={() => router.push("/(tabs)/professionals")}
            >
              <Text style={styles.seeAllText}>Voir tout</Text>
            </TouchableOpacity>
          </View>
          <FlatList
            data={featuredFreelancers}
            renderItem={renderFreelancer}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.freelancersList}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
  },
  welcomeText: {
    fontSize: FontSizes["2xl"],
    fontWeight: "700",
    color: TunaWorkColors.secondary[900],
  },
  subtitle: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[600],
    marginTop: 4,
  },
  notificationButton: {
    padding: Spacing.sm,
  },
  heroSection: {
    marginHorizontal: Spacing.md,
    borderRadius: BorderRadius["2xl"],
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  heroTitle: {
    fontSize: FontSizes["3xl"],
    fontWeight: "700",
    color: "#FFFFFF",
    textAlign: "center",
    marginBottom: Spacing.sm,
  },
  heroSubtitle: {
    fontSize: FontSizes.lg,
    color: "#FFFFFF",
    textAlign: "center",
    opacity: 0.9,
    marginBottom: Spacing.lg,
  },
  heroButtons: {
    flexDirection: "row",
    gap: Spacing.sm,
  },
  heroButton: {
    flex: 1,
  },
  outlineButton: {
    borderColor: "#FFFFFF",
  },
  statsSection: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSizes.xl,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
    marginBottom: Spacing.md,
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Spacing.sm,
  },
  statCard: {
    flex: 1,
    minWidth: "45%",
    alignItems: "center",
  },
  statIcon: {
    marginBottom: Spacing.xs,
  },
  statValue: {
    fontSize: FontSizes["2xl"],
    fontWeight: "700",
    color: TunaWorkColors.primary[500],
  },
  statLabel: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
    textAlign: "center",
  },
  featuredSection: {
    marginBottom: Spacing.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },
  seeAllText: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.primary[500],
    fontWeight: "500",
  },
  freelancersList: {
    paddingHorizontal: Spacing.md,
  },
  freelancerCard: {
    width: 200,
    marginRight: Spacing.md,
  },
  freelancerHeader: {
    position: "relative",
    alignItems: "center",
    marginBottom: Spacing.sm,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  onlineIndicator: {
    position: "absolute",
    bottom: 0,
    right: 70,
  },
  onlineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: "#FFFFFF",
  },
  freelancerName: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
    textAlign: "center",
  },
  freelancerTitle: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
    textAlign: "center",
    marginBottom: Spacing.xs,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Spacing.xs,
  },
  rating: {
    fontSize: FontSizes.sm,
    fontWeight: "500",
    color: TunaWorkColors.secondary[900],
    marginLeft: 4,
  },
  reviewCount: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[500],
    marginLeft: 4,
  },
  hourlyRate: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.primary[500],
    textAlign: "center",
  },
});
