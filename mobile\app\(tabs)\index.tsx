import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  Dimensions,
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { Card } from "../../src/components/ui";
import {
  BorderRadius,
  FontSizes,
  Spacing,
  TunaWorkColors,
} from "../../src/constants";
import { useTranslation } from "../../src/i18n";
import { featuredFreelancers } from "../../src/lib/data";

const { width } = Dimensions.get("window");

export default function HomeScreen() {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");

  const stats = [
    {
      label: t("home.stats.projects"),
      value: "2,500+",
      icon: "briefcase",
      color: "#3B82F6",
    },
    {
      label: t("home.stats.freelancers"),
      value: "1,200+",
      icon: "people",
      color: "#10B981",
    },
    {
      label: t("home.stats.satisfaction"),
      value: "98%",
      icon: "star",
      color: "#F59E0B",
    },
    {
      label: t("home.stats.support"),
      value: "24/7",
      icon: "headset",
      color: "#8B5CF6",
    },
  ];

  const categories = [
    {
      name: "Développement",
      icon: "code-slash",
      color: "#3B82F6",
      count: "450+",
    },
    { name: "Design", icon: "color-palette", color: "#F59E0B", count: "320+" },
    { name: "Marketing", icon: "megaphone", color: "#10B981", count: "280+" },
    { name: "Rédaction", icon: "create", color: "#8B5CF6", count: "190+" },
  ];

  const renderFreelancer = ({
    item,
  }: {
    item: (typeof featuredFreelancers)[0];
  }) => (
    <TouchableOpacity
      style={styles.freelancerCard}
      onPress={() => router.push(`/freelancer/${item.id}` as any)}
    >
      <Card padding="md">
        <View style={styles.freelancerHeader}>
          <Image source={{ uri: item.avatar }} style={styles.avatar} />
          <View style={styles.onlineIndicator}>
            <View
              style={[
                styles.onlineDot,
                { backgroundColor: item.isOnline ? "#10B981" : "#6B7280" },
              ]}
            />
          </View>
        </View>
        <Text style={styles.freelancerName}>
          {item.firstName} {item.lastName}
        </Text>
        <Text style={styles.freelancerTitle}>{item.title}</Text>
        <View style={styles.ratingContainer}>
          <Ionicons name="star" size={16} color="#F59E0B" />
          <Text style={styles.rating}>{item.rating}</Text>
          <Text style={styles.reviewCount}>({item.reviewCount})</Text>
        </View>
        <Text style={styles.hourlyRate}>${item.hourlyRate}/h</Text>
      </Card>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Text style={styles.welcomeText}>Bonjour 👋</Text>
            <Text style={styles.subtitle}>Trouvez votre talent idéal</Text>
          </View>
          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.notificationButton}>
              <Ionicons
                name="notifications-outline"
                size={24}
                color={TunaWorkColors.secondary[600]}
              />
              <View style={styles.notificationBadge}>
                <Text style={styles.notificationBadgeText}>3</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity style={styles.profileButton}>
              <Image
                source={{
                  uri: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
                }}
                style={styles.profileImage}
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Section */}
        <View style={styles.searchSection}>
          <View style={styles.searchContainer}>
            <Ionicons
              name="search"
              size={20}
              color={TunaWorkColors.secondary[400]}
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Rechercher un freelancer ou service..."
              placeholderTextColor={TunaWorkColors.secondary[400]}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            <TouchableOpacity style={styles.filterButton}>
              <Ionicons
                name="options"
                size={20}
                color={TunaWorkColors.primary[500]}
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Categories */}
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Catégories populaires</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesScroll}
          >
            {categories.map((category, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.categoryCard, { borderColor: category.color }]}
              >
                <View
                  style={[
                    styles.categoryIcon,
                    { backgroundColor: category.color + "20" },
                  ]}
                >
                  <Ionicons
                    name={category.icon as any}
                    size={24}
                    color={category.color}
                  />
                </View>
                <Text style={styles.categoryName}>{category.name}</Text>
                <Text style={styles.categoryCount}>{category.count}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Hero Section */}
        <LinearGradient
          colors={["#3B82F6", "#1D4ED8"]}
          style={styles.heroSection}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.heroContent}>
            <View style={styles.heroText}>
              <Text style={styles.heroTitle}>Talents du Congo</Text>
              <Text style={styles.heroSubtitle}>
                Connectez-vous avec les meilleurs freelancers congolais
              </Text>
              <TouchableOpacity style={styles.heroButton}>
                <Text style={styles.heroButtonText}>Commencer maintenant</Text>
                <Ionicons name="arrow-forward" size={20} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
            <View style={styles.heroImage}>
              <View style={styles.floatingCard}>
                <Ionicons name="people" size={24} color="#3B82F6" />
                <Text style={styles.floatingCardText}>1,200+ Freelancers</Text>
              </View>
            </View>
          </View>
        </LinearGradient>

        {/* Stats Section */}
        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Nos statistiques</Text>
          <View style={styles.statsGrid}>
            {stats.map((stat, index) => (
              <Card key={index} style={styles.statCard} padding="md">
                <View
                  style={[
                    styles.statIconContainer,
                    { backgroundColor: stat.color + "20" },
                  ]}
                >
                  <Ionicons
                    name={stat.icon as any}
                    size={24}
                    color={stat.color}
                  />
                </View>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </Card>
            ))}
          </View>
        </View>

        {/* Featured Freelancers */}
        <View style={styles.featuredSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {t("home.featuredFreelancers")}
            </Text>
            <TouchableOpacity
              onPress={() => router.push("/(tabs)/professionals")}
            >
              <Text style={styles.seeAllText}>Voir tout</Text>
            </TouchableOpacity>
          </View>
          <FlatList
            data={featuredFreelancers}
            renderItem={renderFreelancer}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.freelancersList}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F8FAFC",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingTop: Spacing.md,
    paddingBottom: Spacing.sm,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: "row",
    alignItems: "center",
    gap: Spacing.sm,
  },
  welcomeText: {
    fontSize: FontSizes["2xl"],
    fontWeight: "700",
    color: TunaWorkColors.secondary[900],
  },
  subtitle: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[600],
    marginTop: 4,
  },
  notificationButton: {
    position: "relative",
    padding: Spacing.sm,
    backgroundColor: "#FFFFFF",
    borderRadius: BorderRadius.lg,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  notificationBadge: {
    position: "absolute",
    top: 6,
    right: 6,
    backgroundColor: "#EF4444",
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  notificationBadgeText: {
    color: "#FFFFFF",
    fontSize: 10,
    fontWeight: "600",
  },
  profileButton: {
    padding: 2,
  },
  profileImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: TunaWorkColors.primary[500],
  },
  // Search Section
  searchSection: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    borderRadius: BorderRadius.xl,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  searchIcon: {
    marginRight: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[900],
    paddingVertical: Spacing.xs,
  },
  filterButton: {
    padding: Spacing.xs,
    backgroundColor: TunaWorkColors.primary[50],
    borderRadius: BorderRadius.md,
    marginLeft: Spacing.sm,
  },
  // Categories Section
  categoriesSection: {
    marginBottom: Spacing.lg,
  },
  categoriesScroll: {
    paddingHorizontal: Spacing.md,
  },
  categoryCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: BorderRadius.xl,
    padding: Spacing.md,
    marginRight: Spacing.sm,
    alignItems: "center",
    minWidth: 100,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: Spacing.xs,
  },
  categoryName: {
    fontSize: FontSizes.sm,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
    textAlign: "center",
    marginBottom: 2,
  },
  categoryCount: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.secondary[500],
    textAlign: "center",
  },
  // Hero Section
  heroSection: {
    marginHorizontal: Spacing.md,
    borderRadius: BorderRadius["2xl"],
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    overflow: "hidden",
  },
  heroContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  heroText: {
    flex: 1,
    paddingRight: Spacing.md,
  },
  heroTitle: {
    fontSize: FontSizes["2xl"],
    fontWeight: "700",
    color: "#FFFFFF",
    marginBottom: Spacing.xs,
  },
  heroSubtitle: {
    fontSize: FontSizes.base,
    color: "rgba(255, 255, 255, 0.9)",
    marginBottom: Spacing.md,
    lineHeight: 22,
  },
  heroButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
    gap: Spacing.xs,
  },
  heroButtonText: {
    color: "#FFFFFF",
    fontSize: FontSizes.base,
    fontWeight: "600",
  },
  heroImage: {
    alignItems: "center",
    justifyContent: "center",
  },
  floatingCard: {
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    borderRadius: BorderRadius.lg,
    padding: Spacing.sm,
    flexDirection: "row",
    alignItems: "center",
    gap: Spacing.xs,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  floatingCardText: {
    fontSize: FontSizes.sm,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
  },
  heroSubtitle: {
    fontSize: FontSizes.lg,
    color: "#FFFFFF",
    textAlign: "center",
    opacity: 0.9,
    marginBottom: Spacing.lg,
  },
  heroButtons: {
    flexDirection: "row",
    gap: Spacing.sm,
  },
  heroButton: {
    flex: 1,
  },
  outlineButton: {
    borderColor: "#FFFFFF",
  },
  statsSection: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSizes.xl,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
    marginBottom: Spacing.md,
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Spacing.sm,
  },
  statCard: {
    flex: 1,
    minWidth: "45%",
    alignItems: "center",
  },
  statIcon: {
    marginBottom: Spacing.xs,
  },
  statValue: {
    fontSize: FontSizes["2xl"],
    fontWeight: "700",
    color: TunaWorkColors.primary[500],
  },
  statLabel: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
    textAlign: "center",
  },
  featuredSection: {
    marginBottom: Spacing.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },
  seeAllText: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.primary[500],
    fontWeight: "500",
  },
  freelancersList: {
    paddingHorizontal: Spacing.md,
  },
  freelancerCard: {
    width: 200,
    marginRight: Spacing.md,
  },
  freelancerHeader: {
    position: "relative",
    alignItems: "center",
    marginBottom: Spacing.sm,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  onlineIndicator: {
    position: "absolute",
    bottom: 0,
    right: 70,
  },
  onlineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: "#FFFFFF",
  },
  freelancerName: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
    textAlign: "center",
  },
  freelancerTitle: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
    textAlign: "center",
    marginBottom: Spacing.xs,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Spacing.xs,
  },
  rating: {
    fontSize: FontSizes.sm,
    fontWeight: "500",
    color: TunaWorkColors.secondary[900],
    marginLeft: 4,
  },
  reviewCount: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[500],
    marginLeft: 4,
  },
  hourlyRate: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.primary[500],
    textAlign: "center",
  },
});
