import React from "react";
import { Dimensions, StyleSheet, Text, View, TouchableOpacity } from "react-native";

import { useTranslation } from "../../i18n";
import { OnboardingSlideProps } from "./types";

const { width, height } = Dimensions.get("window");

export function OnboardingSlide({
  slide,
  isActive,
  onNext,
  onSkip,
  currentIndex,
  totalSlides,
}: OnboardingSlideProps) {
  const { t } = useTranslation();

  const getBackgroundColor = () => {
    switch (currentIndex) {
      case 0:
        return '#3B82F6';
      case 1:
        return '#10B981';
      case 2:
        return '#F59E0B';
      default:
        return '#3B82F6';
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: getBackgroundColor() }]}>
      {/* Skip Button */}
      {onSkip && currentIndex < totalSlides - 1 && (
        <View style={styles.skipContainer}>
          <TouchableOpacity onPress={onSkip} style={styles.skipButton}>
            <Text style={styles.skipText}>{t('onboarding.skip')}</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Progress Indicators */}
      <View style={styles.progressContainer}>
        {Array.from({ length: totalSlides }).map((_, index) => (
          <View
            key={index}
            style={[
              styles.progressDot,
              index === currentIndex && styles.activeProgressDot,
            ]}
          />
        ))}
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Icon placeholder */}
        <View style={styles.iconContainer}>
          <Text style={styles.iconText}>
            {currentIndex === 0 ? '👥' : currentIndex === 1 ? '🚀' : '🛡️'}
          </Text>
        </View>

        {/* Text Content */}
        <View style={styles.textContainer}>
          {slide.subtitle && (
            <Text style={styles.subtitle}>{slide.subtitle}</Text>
          )}
          <Text style={styles.title}>{slide.title}</Text>
          <Text style={styles.description}>{slide.description}</Text>
        </View>

        {/* Action Button */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity onPress={onNext} style={styles.button}>
            <Text style={styles.buttonText}>
              {slide.isLast ? t('onboarding.getStarted') : slide.buttonText}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Swipe Hint */}
        {!slide.isLast && (
          <Text style={styles.swipeHint}>{t('onboarding.swipe_hint')}</Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width,
    height,
  },
  skipContainer: {
    position: 'absolute',
    top: 60,
    right: 16,
    zIndex: 10,
  },
  skipButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  skipText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  progressContainer: {
    position: 'absolute',
    top: 120,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 4,
    zIndex: 10,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  activeProgressDot: {
    backgroundColor: '#FFFFFF',
    width: 24,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 180,
    paddingBottom: 100,
  },
  iconContainer: {
    marginBottom: 48,
  },
  iconText: {
    fontSize: 80,
    textAlign: 'center',
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 48,
  },
  subtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '500',
  },
  title: {
    fontSize: 30,
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 36,
  },
  description: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 25,
    paddingHorizontal: 8,
  },
  buttonContainer: {
    width: '100%',
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  button: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  buttonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  swipeHint: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
  },
});
