{"version": 3, "sources": ["constants.ts"], "names": ["Hammer", "State", "CONTENT_TOUCHES_DELAY", "CONTENT_TOUCHES_QUICK_TAP_END_DELAY", "MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD", "MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD", "DEG_RAD", "Math", "PI", "EventMap", "INPUT_START", "BEGAN", "INPUT_MOVE", "ACTIVE", "INPUT_END", "END", "INPUT_CANCEL", "FAILED", "Direction", "RIGHT", "LEFT", "UP", "DOWN", "DirectionMap", "DIRECTION_RIGHT", "DIRECTION_LEFT", "DIRECTION_UP", "DIRECTION_DOWN", "HammerInputNames", "HammerDirectionNames", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "DIRECTION_NONE", "DIRECTION_ALL"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,gBAAnB;AAEA,SAASC,KAAT,QAAsB,UAAtB;AAEA,OAAO,MAAMC,qBAAqB,GAAG,GAA9B;AACP,OAAO,MAAMC,mCAAmC,GAAG,EAA5C;AACP,OAAO,MAAMC,oCAAoC,GAAG,GAA7C;AACP,OAAO,MAAMC,uCAAuC,GAAG,CAAhD;AACP,OAAO,MAAMC,OAAO,GAAGC,IAAI,CAACC,EAAL,GAAU,GAA1B,C,CAEP;;AACA,OAAO,MAAMC,QAAQ,GAAG;AACtB,GAACT,MAAM,CAACU,WAAR,GAAsBT,KAAK,CAACU,KADN;AAEtB,GAACX,MAAM,CAACY,UAAR,GAAqBX,KAAK,CAACY,MAFL;AAGtB,GAACb,MAAM,CAACc,SAAR,GAAoBb,KAAK,CAACc,GAHJ;AAItB,GAACf,MAAM,CAACgB,YAAR,GAAuBf,KAAK,CAACgB;AAJP,CAAjB;AAOP,OAAO,MAAMC,SAAS,GAAG;AACvBC,EAAAA,KAAK,EAAE,CADgB;AAEvBC,EAAAA,IAAI,EAAE,CAFiB;AAGvBC,EAAAA,EAAE,EAAE,CAHmB;AAIvBC,EAAAA,IAAI,EAAE;AAJiB,CAAlB;AAOP,OAAO,MAAMC,YAAY,GAAG;AAC1B,GAACvB,MAAM,CAACwB,eAAR,GAA0BN,SAAS,CAACC,KADV;AAE1B,GAACnB,MAAM,CAACyB,cAAR,GAAyBP,SAAS,CAACE,IAFT;AAG1B,GAACpB,MAAM,CAAC0B,YAAR,GAAuBR,SAAS,CAACG,EAHP;AAI1B,GAACrB,MAAM,CAAC2B,cAAR,GAAyBT,SAAS,CAACI;AAJT,CAArB;AAOP,OAAO,MAAMM,gBAAgB,GAAG;AAC9B,GAAC5B,MAAM,CAACU,WAAR,GAAsB,OADQ;AAE9B,GAACV,MAAM,CAACY,UAAR,GAAqB,MAFS;AAG9B,GAACZ,MAAM,CAACc,SAAR,GAAoB,KAHU;AAI9B,GAACd,MAAM,CAACgB,YAAR,GAAuB;AAJO,CAAzB;AAMP,OAAO,MAAMa,oBAAoB,GAAG;AAClC,GAAC7B,MAAM,CAAC8B,oBAAR,GAA+B,YADG;AAElC,GAAC9B,MAAM,CAAC0B,YAAR,GAAuB,IAFW;AAGlC,GAAC1B,MAAM,CAAC2B,cAAR,GAAyB,MAHS;AAIlC,GAAC3B,MAAM,CAAC+B,kBAAR,GAA6B,UAJK;AAKlC,GAAC/B,MAAM,CAACgC,cAAR,GAAyB,MALS;AAMlC,GAAChC,MAAM,CAACiC,aAAR,GAAwB,KANU;AAOlC,GAACjC,MAAM,CAACwB,eAAR,GAA0B,OAPQ;AAQlC,GAACxB,MAAM,CAACyB,cAAR,GAAyB;AARS,CAA7B", "sourcesContent": ["import Hammer from '@egjs/hammerjs';\n\nimport { State } from '../State';\n\nexport const CONTENT_TOUCHES_DELAY = 240;\nexport const CONTENT_TOUCHES_QUICK_TAP_END_DELAY = 50;\nexport const MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD = 0.1;\nexport const MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD = 7;\nexport const DEG_RAD = Math.PI / 180;\n\n// Map Hammer values to RNGH\nexport const EventMap = {\n  [Hammer.INPUT_START]: State.BEGAN,\n  [Hammer.INPUT_MOVE]: State.ACTIVE,\n  [Hammer.INPUT_END]: State.END,\n  [Hammer.INPUT_CANCEL]: State.FAILED,\n} as const;\n\nexport const Direction = {\n  RIGHT: 1,\n  LEFT: 2,\n  UP: 4,\n  DOWN: 8,\n};\n\nexport const DirectionMap = {\n  [Hammer.DIRECTION_RIGHT]: Direction.RIGHT,\n  [Hammer.DIRECTION_LEFT]: Direction.LEFT,\n  [Hammer.DIRECTION_UP]: Direction.UP,\n  [Hammer.DIRECTION_DOWN]: Direction.DOWN,\n};\n\nexport const HammerInputNames = {\n  [Hammer.INPUT_START]: 'START',\n  [Hammer.INPUT_MOVE]: 'MOVE',\n  [Hammer.INPUT_END]: 'END',\n  [Hammer.INPUT_CANCEL]: 'CANCEL',\n};\nexport const HammerDirectionNames = {\n  [Hammer.DIRECTION_HORIZONTAL]: 'HORIZONTAL',\n  [Hammer.DIRECTION_UP]: 'UP',\n  [Hammer.DIRECTION_DOWN]: 'DOWN',\n  [Hammer.DIRECTION_VERTICAL]: 'VERTICAL',\n  [Hammer.DIRECTION_NONE]: 'NONE',\n  [Hammer.DIRECTION_ALL]: 'ALL',\n  [Hammer.DIRECTION_RIGHT]: 'RIGHT',\n  [Hammer.DIRECTION_LEFT]: 'LEFT',\n};\n"]}