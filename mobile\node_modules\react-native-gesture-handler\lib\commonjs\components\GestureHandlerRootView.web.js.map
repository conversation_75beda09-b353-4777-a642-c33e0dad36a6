{"version": 3, "sources": ["GestureHandlerRootView.web.tsx"], "names": ["GestureHandlerRootView", "style", "rest", "styles", "container", "StyleSheet", "create", "flex"], "mappings": ";;;;;;;AAAA;;AAEA;;AACA;;;;;;;;;;AAKe,SAASA,sBAAT,CAAgC;AAC7CC,EAAAA,KAD6C;AAE7C,KAAGC;AAF0C,CAAhC,EAGiB;AAC9B,sBACE,oBAAC,sCAAD,CAA+B,QAA/B;AAAwC,IAAA,KAAK;AAA7C,kBACE,oBAAC,iBAAD;AAAM,IAAA,KAAK,EAAED,KAAF,aAAEA,KAAF,cAAEA,KAAF,GAAWE,MAAM,CAACC;AAA7B,KAA4CF,IAA5C,EADF,CADF;AAKD;;AAED,MAAMC,MAAM,GAAGE,wBAAWC,MAAX,CAAkB;AAC/BF,EAAAA,SAAS,EAAE;AAAEG,IAAAA,IAAI,EAAE;AAAR;AADoB,CAAlB,CAAf", "sourcesContent": ["import * as React from 'react';\nimport { PropsWithChildren } from 'react';\nimport { View, ViewProps, StyleSheet } from 'react-native';\nimport GestureHandlerRootViewContext from '../GestureHandlerRootViewContext';\n\nexport interface GestureHandlerRootViewProps\n  extends PropsWithChildren<ViewProps> {}\n\nexport default function GestureHandlerRootView({\n  style,\n  ...rest\n}: GestureHandlerRootViewProps) {\n  return (\n    <GestureHandlerRootViewContext.Provider value>\n      <View style={style ?? styles.container} {...rest} />\n    </GestureHandlerRootViewContext.Provider>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: { flex: 1 },\n});\n"]}