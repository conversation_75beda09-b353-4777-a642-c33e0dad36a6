{"version": 3, "names": ["React", "extractProps", "propsAndStyles", "extractTransform", "extractText", "setTSpan", "pickNotNil", "<PERSON><PERSON><PERSON>", "RNSVGTSpan", "TSpan", "displayName", "setNativeProps", "props", "matrix", "prop", "Object", "assign", "root", "render", "x", "y", "ref", "refMethod", "createElement"], "sourceRoot": "../../../src", "sources": ["elements/TSpan.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,IAAIC,cAAc,QAAQ,6BAA6B;AAC1E,OAAOC,gBAAgB,MAAM,iCAAiC;AAE9D,OAAOC,WAAW,IAAIC,QAAQ,QAAQ,4BAA4B;AAClE,SAASC,UAAU,QAAQ,aAAa;AACxC,OAAOC,KAAK,MAAM,SAAS;AAQ3B,OAAOC,UAAU,MAAM,gCAAgC;AAYvD,eAAe,MAAMC,KAAK,SAASF,KAAK,CAAa;EACnD,OAAOG,WAAW,GAAG,OAAO;EAE5BC,cAAc,GACZC,KAGC,IACE;IACH,MAAMC,MAAM,GAAG,CAACD,KAAK,CAACC,MAAM,IAAIV,gBAAgB,CAACS,KAAK,CAAC;IACvD,IAAIC,MAAM,EAAE;MACVD,KAAK,CAACC,MAAM,GAAGA,MAAM;IACvB;IACA,MAAMC,IAAI,GAAGZ,cAAc,CAACU,KAAK,CAAC;IAClCG,MAAM,CAACC,MAAM,CAACF,IAAI,EAAER,UAAU,CAACF,WAAW,CAACU,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;IACzD,IAAI,CAACG,IAAI,IAAI,IAAI,CAACA,IAAI,CAACN,cAAc,CAACG,IAAI,CAAC;EAC7C,CAAC;EAEDI,MAAMA,CAAA,EAAG;IACP,MAAMJ,IAAI,GAAGZ,cAAc,CAAC,IAAI,CAACU,KAAK,CAAC;IACvC,MAAMA,KAAK,GAAGX,YAAY,CACxB;MACE,GAAGa,IAAI;MACPK,CAAC,EAAE,IAAI;MACPC,CAAC,EAAE;IACL,CAAC,EACD,IACF,CAAC;IACDL,MAAM,CAACC,MAAM,CAACJ,KAAK,EAAER,WAAW,CAACU,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9CF,KAAK,CAACS,GAAG,GAAG,IAAI,CAACC,SAAiD;IAClE,oBAAOtB,KAAA,CAAAuB,aAAA,CAACf,UAAU,EAAKI,KAAQ,CAAC;EAClC;AACF;AAEAP,QAAQ,CAACI,KAAK,CAAC", "ignoreList": []}