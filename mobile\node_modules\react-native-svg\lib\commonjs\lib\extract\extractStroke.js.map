{"version": 3, "names": ["_extractBrush", "_interopRequireDefault", "require", "_extractOpacity", "_extractLengthList", "e", "__esModule", "default", "caps", "butt", "square", "round", "joins", "miter", "bevel", "vectorEffects", "none", "nonScalingStroke", "inherit", "uri", "extractStroke", "o", "props", "inherited", "stroke", "strokeOpacity", "strokeLinecap", "strokeLinejoin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeWidth", "strokeDashoffset", "strokeMiterlimit", "vectorEffect", "push", "extractBrush", "extractOpacity", "strokeDash", "extractLengthList", "length", "concat", "parseFloat"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractStroke.ts"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,eAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,kBAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAoD,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAGpD,MAAMG,IAAI,GAAG;EACXC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,KAAK,GAAG;EACZC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,CAAC;EACRH,KAAK,EAAE;AACT,CAAC;AAED,MAAMI,aAAa,GAAG;EACpBC,IAAI,EAAE,CAAC;EACPT,OAAO,EAAE,CAAC;EACVU,gBAAgB,EAAE,CAAC;EACnB,oBAAoB,EAAE,CAAC;EACvBC,OAAO,EAAE,CAAC;EACVC,GAAG,EAAE;AACP,CAAC;AAEc,SAASC,aAAaA,CACnCC,CAAiB,EACjBC,KAAkB,EAClBC,SAAmB,EACnB;EACA,MAAM;IACJC,MAAM;IACNC,aAAa;IACbC,aAAa;IACbC,cAAc;IACdC,eAAe;IACfC,WAAW;IACXC,gBAAgB;IAChBC,gBAAgB;IAChBC;EACF,CAAC,GAAGV,KAAK;EAET,IAAIE,MAAM,IAAI,IAAI,EAAE;IAClBD,SAAS,CAACU,IAAI,CAAC,QAAQ,CAAC;IACxBZ,CAAC,CAACG,MAAM,GAAG,IAAAU,qBAAY,EAACV,MAAM,CAAC;EACjC;EACA,IAAIK,WAAW,IAAI,IAAI,EAAE;IACvBN,SAAS,CAACU,IAAI,CAAC,aAAa,CAAC;IAC7BZ,CAAC,CAACQ,WAAW,GAAGA,WAAW;EAC7B;EACA,IAAIJ,aAAa,IAAI,IAAI,EAAE;IACzBF,SAAS,CAACU,IAAI,CAAC,eAAe,CAAC;IAC/BZ,CAAC,CAACI,aAAa,GAAG,IAAAU,uBAAc,EAACV,aAAa,CAAC;EACjD;EACA,IAAIG,eAAe,IAAI,IAAI,EAAE;IAC3BL,SAAS,CAACU,IAAI,CAAC,iBAAiB,CAAC;IACjC,MAAMG,UAAU,GACd,CAACR,eAAe,IAAIA,eAAe,KAAK,MAAM,GAC1C,IAAI,GACJ,IAAAS,0BAAiB,EAACT,eAAe,CAAC;IACxCP,CAAC,CAACO,eAAe,GACfQ,UAAU,IAAIA,UAAU,CAACE,MAAM,GAAG,CAAC,KAAK,CAAC,GACrCF,UAAU,CAACG,MAAM,CAACH,UAAU,CAAC,GAC7BA,UAAU;EAClB;EACA,IAAIN,gBAAgB,IAAI,IAAI,EAAE;IAC5BP,SAAS,CAACU,IAAI,CAAC,kBAAkB,CAAC;IAClCZ,CAAC,CAACS,gBAAgB,GAChBF,eAAe,IAAIE,gBAAgB,GAAG,CAACA,gBAAgB,IAAI,CAAC,GAAG,IAAI;EACvE;EACA,IAAIJ,aAAa,IAAI,IAAI,EAAE;IACzBH,SAAS,CAACU,IAAI,CAAC,eAAe,CAAC;IAC/BZ,CAAC,CAACK,aAAa,GAAIA,aAAa,IAAIlB,IAAI,CAACkB,aAAa,CAAC,IAAK,CAAC;EAC/D;EACA,IAAIC,cAAc,IAAI,IAAI,EAAE;IAC1BJ,SAAS,CAACU,IAAI,CAAC,gBAAgB,CAAC;IAChCZ,CAAC,CAACM,cAAc,GAAIA,cAAc,IAAIf,KAAK,CAACe,cAAc,CAAC,IAAK,CAAC;EACnE;EACA,IAAII,gBAAgB,IAAI,IAAI,EAAE;IAC5BR,SAAS,CAACU,IAAI,CAAC,kBAAkB,CAAC;IAClCZ,CAAC,CAACU,gBAAgB,GAChB,CAACA,gBAAgB,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,GACrDS,UAAU,CAACT,gBAAgB,CAAC,GAC5BA,gBAAgB,KAAK,CAAC;EAC9B;EACA,IAAIC,YAAY,IAAI,IAAI,EAAE;IACxBX,CAAC,CAACW,YAAY,GAAIA,YAAY,IAAIjB,aAAa,CAACiB,YAAY,CAAC,IAAK,CAAC;EACrE;AACF", "ignoreList": []}