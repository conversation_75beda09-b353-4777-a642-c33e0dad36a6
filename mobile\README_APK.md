# 🚀 Génération APK TunaWork Mobile

## 📋 Instructions Rapides

### 1. Installation d'EAS CLI
Ouvrez un terminal/PowerShell **en tant qu'administrateur** et exécutez :
```bash
npm install -g @expo/eas-cli
```

### 2. Connexion à Expo
```bash
eas login
```
Créez un compte sur [expo.dev](https://expo.dev/) si vous n'en avez pas.

### 3. Configuration du projet
Dans le dossier `mobile/`, exécutez :
```bash
eas build:configure
```

### 4. Génération de l'APK
```bash
# APK de test (recommandé pour commencer)
eas build --platform android --profile preview

# APK de production
eas build --platform android --profile production
```

## 📱 Récupération de l'APK

1. **Dashboard Expo** : [expo.dev](https://expo.dev/)
2. Allez dans **Projects** → **tunawork-mobile** → **Builds**
3. Téléchargez l'APK une fois le build terminé
4. Installez sur votre appareil Android

## ⚡ Scripts NPM Disponibles

```bash
# Génération APK de test
npm run build:android

# Génération APK de production
npm run build:android:production
```

## 🎯 Fonctionnalités de l'App

### ✅ Implémentées
- **Page d'accueil** moderne et attractive
- **Navigation** avec 5 onglets principaux
- **Freelancers artisanaux** : menuisiers, couturières, chauffeurs, lavandières, maçons
- **Chat professionnel** plus avancé que WhatsApp/Facebook
- **Profils détaillés** avec portfolios et avis
- **Recherche et filtres** par catégories
- **Design responsive** et moderne
- **Données authentiques** congolaises

### 🇨🇩 Métiers Congolais
- **Menuisiers** (Jean-Baptiste Mukendi)
- **Couturières** (Mama Chantal)
- **Chauffeurs** (Papa Dieudonné)
- **Lavandières** (Mama Esperance)
- **Maçons** (Frère Augustin)

### 💰 Tarifs Locaux
- Lavanderie : 2$/heure
- Transport : 3$/heure
- Couture : 5$/heure
- Maçonnerie : 6$/heure
- Menuiserie : 8$/heure

## 🔧 Configuration Technique

### Fichiers Créés
- ✅ `eas.json` - Configuration EAS Build
- ✅ `build-android.ps1` - Script PowerShell
- ✅ `build-android.bat` - Script Batch
- ✅ `BUILD_GUIDE.md` - Guide détaillé

### Package.json Mis à Jour
```json
{
  "scripts": {
    "build:android": "eas build --platform android --profile preview",
    "build:android:production": "eas build --platform android --profile production"
  }
}
```

## 🎨 Design et UX

### Couleurs Principales
- **Primaire** : Bleu moderne (#007AFF)
- **Secondaire** : Vert (#10B981)
- **Accent** : Orange (#F59E0B)

### Icônes
- **Menuiserie** : 🔨 Marteau
- **Couture** : 👕 Vêtement
- **Transport** : 🚗 Voiture
- **Lavanderie** : 💧 Eau
- **Maçonnerie** : 🏠 Maison

## 📊 Temps de Build Estimé

- **Premier build** : 10-15 minutes
- **Builds suivants** : 5-10 minutes
- **Avec cache** : 3-5 minutes

## 🚨 Résolution de Problèmes

### Si EAS CLI ne s'installe pas
```bash
# Essayez avec npx
npx @expo/eas-cli@latest login
npx @expo/eas-cli@latest build --platform android --profile preview
```

### Si vous n'avez pas de compte Expo
1. Allez sur [expo.dev](https://expo.dev/)
2. Cliquez sur "Sign up"
3. Créez votre compte
4. Vérifiez votre email
5. Utilisez `eas login`

### Si le build échoue
1. Vérifiez les logs dans le dashboard Expo
2. Assurez-vous que tous les fichiers sont sauvegardés
3. Relancez le build

## 📞 Support

- **Documentation** : [docs.expo.dev](https://docs.expo.dev/)
- **Forum** : [forums.expo.dev](https://forums.expo.dev/)
- **Discord** : [discord.gg/expo](https://discord.gg/expo)

---

**TunaWork Mobile** - Connecter les artisans congolais avec leurs clients 🇨🇩✨
