{"version": 3, "sources": ["attachHandlers.ts"], "names": ["attachHandlers", "preparedGesture", "gestureConfig", "gestures<PERSON>oAtta<PERSON>", "viewTag", "webEventHandlersRef", "initialize", "isMounted", "prepare", "handler", "RNGestureHandlerModule", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "config", "ALLOWED_PROPS", "testId", "updateGestureHandler", "gesture", "actionType", "shouldUseReanimated", "ActionType", "REANIMATED_WORKLET", "JS_FUNCTION_NEW_API", "Platform", "OS", "attachGestureHandler", "JS_FUNCTION_OLD_API", "MountRegistry", "gestureWillMount", "attachedGestures", "animatedHandlers", "isAnimatedGesture", "g", "value", "filter", "map", "handlers"], "mappings": ";;;;;;;AAEA;;AACA;;AACA;;AAEA;;AACA;;AAEA;;AAEA;;AAKA;;;;AAUO,SAASA,cAAT,CAAwB;AAC7BC,EAAAA,eAD6B;AAE7BC,EAAAA,aAF6B;AAG7BC,EAAAA,gBAH6B;AAI7BC,EAAAA,OAJ6B;AAK7BC,EAAAA;AAL6B,CAAxB,EAMkB;AACvBH,EAAAA,aAAa,CAACI,UAAd,GADuB,CAGvB;AACA;;AACA,0CAAiB,MAAM;AACrB,QAAI,CAACL,eAAe,CAACM,SAArB,EAAgC;AAC9B;AACD;;AACDL,IAAAA,aAAa,CAACM,OAAd;AACD,GALD;;AAOA,OAAK,MAAMC,OAAX,IAAsBN,gBAAtB,EAAwC;AACtC,kDAAiCM,OAAjC;;AACAC,oCAAuBC,oBAAvB,CACEF,OAAO,CAACG,WADV,EAEEH,OAAO,CAACI,UAFV,EAGE,yBAAaJ,OAAO,CAACK,MAArB,EAA6BC,qBAA7B,CAHF;;AAMA,2CAAgBN,OAAO,CAACI,UAAxB,EAAoCJ,OAApC,EAA6CA,OAAO,CAACK,MAAR,CAAeE,MAA5D;AACD,GArBsB,CAuBvB;AACA;;;AACA,0CAAiB,MAAM;AACrB,QAAI,CAACf,eAAe,CAACM,SAArB,EAAgC;AAC9B;AACD;;AACD,SAAK,MAAME,OAAX,IAAsBN,gBAAtB,EAAwC;AACtCO,sCAAuBO,oBAAvB,CACER,OAAO,CAACI,UADV,EAEE,yBACEJ,OAAO,CAACK,MADV,EAEEC,qBAFF,EAGE,qCAAwBN,OAAxB,CAHF,CAFF;AAQD;;AAED;AACD,GAhBD;;AAkBA,OAAK,MAAMS,OAAX,IAAsBf,gBAAtB,EAAwC;AACtC,UAAMgB,UAAU,GAAGD,OAAO,CAACE,mBAAR,GACfC,uBAAWC,kBADI,GAEfD,uBAAWE,mBAFf;;AAIA,QAAIC,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AAEvBf,sCAAuBgB,oBADzB,CAGER,OAAO,CAACL,UAHV,EAIET,OAJF,EAKEiB,uBAAWM,mBALb,EAKkC;AAChCtB,MAAAA,mBANF;AAQD,KATD,MASO;AACLK,sCAAuBgB,oBAAvB,CACER,OAAO,CAACL,UADV,EAEET,OAFF,EAGEe,UAHF;AAKD;;AAEDS,iCAAcC,gBAAd,CAA+BX,OAA/B;AACD;;AAEDjB,EAAAA,eAAe,CAAC6B,gBAAhB,GAAmC3B,gBAAnC;;AAEA,MAAIF,eAAe,CAAC8B,gBAApB,EAAsC;AACpC,UAAMC,iBAAiB,GAAIC,CAAD,IAAoBA,CAAC,CAACb,mBAAhD;;AAEAnB,IAAAA,eAAe,CAAC8B,gBAAhB,CAAiCG,KAAjC,GAAyC/B,gBAAgB,CACtDgC,MADsC,CAC/BH,iBAD+B,EAEtCI,GAFsC,CAEjCH,CAAD,IAAOA,CAAC,CAACI,QAFyB,CAAzC;AAKD;AACF", "sourcesContent": ["import React from 'react';\nimport { GestureType, HandlerCallbacks } from '../gesture';\nimport { registerHandler } from '../../handlersRegistry';\nimport RNGestureHandlerModule from '../../../RNGestureHandlerModule';\nimport { filterConfig, scheduleFlushOperations } from '../../utils';\nimport { ComposedGesture } from '../gestureComposition';\nimport { ActionType } from '../../../ActionType';\nimport { Platform } from 'react-native';\nimport type RNGestureHandlerModuleWeb from '../../../RNGestureHandlerModule.web';\nimport { ghQueueMicrotask } from '../../../ghQueueMicrotask';\nimport { AttachedGestureState, WebEventHandler } from './types';\nimport {\n  extractGestureRelations,\n  checkGestureCallbacksForWorklets,\n  ALLOWED_PROPS,\n} from './utils';\nimport { MountRegistry } from '../../../mountRegistry';\n\ninterface AttachHandlersConfig {\n  preparedGesture: AttachedGestureState;\n  gestureConfig: ComposedGesture | GestureType;\n  gesturesToAttach: GestureType[];\n  viewTag: number;\n  webEventHandlersRef: React.RefObject<WebEventHandler>;\n}\n\nexport function attachHandlers({\n  preparedGesture,\n  gestureConfig,\n  gesturesToAttach,\n  viewTag,\n  webEventHandlersRef,\n}: AttachHandlersConfig) {\n  gestureConfig.initialize();\n\n  // Use queueMicrotask to extract handlerTags, because all refs should be initialized\n  // when it's ran\n  ghQueueMicrotask(() => {\n    if (!preparedGesture.isMounted) {\n      return;\n    }\n    gestureConfig.prepare();\n  });\n\n  for (const handler of gesturesToAttach) {\n    checkGestureCallbacksForWorklets(handler);\n    RNGestureHandlerModule.createGestureHandler(\n      handler.handlerName,\n      handler.handlerTag,\n      filterConfig(handler.config, ALLOWED_PROPS)\n    );\n\n    registerHandler(handler.handlerTag, handler, handler.config.testId);\n  }\n\n  // Use queueMicrotask to extract handlerTags, because all refs should be initialized\n  // when it's ran\n  ghQueueMicrotask(() => {\n    if (!preparedGesture.isMounted) {\n      return;\n    }\n    for (const handler of gesturesToAttach) {\n      RNGestureHandlerModule.updateGestureHandler(\n        handler.handlerTag,\n        filterConfig(\n          handler.config,\n          ALLOWED_PROPS,\n          extractGestureRelations(handler)\n        )\n      );\n    }\n\n    scheduleFlushOperations();\n  });\n\n  for (const gesture of gesturesToAttach) {\n    const actionType = gesture.shouldUseReanimated\n      ? ActionType.REANIMATED_WORKLET\n      : ActionType.JS_FUNCTION_NEW_API;\n\n    if (Platform.OS === 'web') {\n      (\n        RNGestureHandlerModule.attachGestureHandler as typeof RNGestureHandlerModuleWeb.attachGestureHandler\n      )(\n        gesture.handlerTag,\n        viewTag,\n        ActionType.JS_FUNCTION_OLD_API, // Ignored on web\n        webEventHandlersRef\n      );\n    } else {\n      RNGestureHandlerModule.attachGestureHandler(\n        gesture.handlerTag,\n        viewTag,\n        actionType\n      );\n    }\n\n    MountRegistry.gestureWillMount(gesture);\n  }\n\n  preparedGesture.attachedGestures = gesturesToAttach;\n\n  if (preparedGesture.animatedHandlers) {\n    const isAnimatedGesture = (g: GestureType) => g.shouldUseReanimated;\n\n    preparedGesture.animatedHandlers.value = gesturesToAttach\n      .filter(isAnimatedGesture)\n      .map((g) => g.handlers) as unknown as HandlerCallbacks<\n      Record<string, unknown>\n    >[];\n  }\n}\n"]}