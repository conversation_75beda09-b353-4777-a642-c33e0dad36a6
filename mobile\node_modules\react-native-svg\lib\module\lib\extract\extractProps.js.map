{"version": 3, "names": ["extractFill", "extractStroke", "extractTransform", "extractResponder", "extractOpacity", "idPattern", "clipRules", "evenodd", "nonzero", "propsAndStyles", "props", "style", "Array", "isArray", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON>", "marker", "undefined", "matched", "match", "extractProps", "ref", "id", "opacity", "onLayout", "clipPath", "clipRule", "display", "mask", "filter", "markerStart", "markerMid", "markerEnd", "testID", "accessibilityLabel", "accessible", "extracted", "inherited", "color", "length", "propList", "matrix", "name", "String", "console", "warn", "extract", "instance", "withoutXY", "x", "y"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractProps.ts"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,SAAS,QAAQ,SAAS;AAYnC,MAAMC,SAA+C,GAAG;EACtDC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AAED,OAAO,SAASC,cAAcA,CAACC,KAAwC,EAAE;EACvE,MAAM;IAAEC;EAAM,CAAC,GAAGD,KAAK;EACvB,OAAO,CAACC,KAAK,GACTD,KAAK,GACL;IACE,IAAIE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAGJ,KAAK,CAAC,GAAGA,KAAK,CAAC;IAC/D,GAAGD;EACL,CAAC;AACP;AAEA,SAASM,SAASA,CAACC,MAAe,EAAE;EAClC,IAAI,CAACA,MAAM,EAAE;IACX,OAAOC,SAAS;EAClB;EACA,MAAMC,OAAO,GAAGF,MAAM,CAACG,KAAK,CAACf,SAAS,CAAC;EACvC,OAAOc,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAGD,SAAS;AACzC;AAEA,eAAe,SAASG,YAAYA,CAClCX,KAoBW,EACXY,GAAW,EACX;EACA,MAAM;IACJC,EAAE;IACFC,OAAO;IACPC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,OAAO;IACPC,IAAI;IACJC,MAAM;IACNb,MAAM;IACNc,WAAW,GAAGd,MAAM;IACpBe,SAAS,GAAGf,MAAM;IAClBgB,SAAS,GAAGhB,MAAM;IAClBiB,MAAM;IACNC,kBAAkB;IAClBC;EACF,CAAC,GAAG1B,KAAK;EACT,MAAM2B,SAAyB,GAAG,CAAC,CAAC;EAEpC,MAAMC,SAAmB,GAAG,EAAE;EAC9BnC,gBAAgB,CAACkC,SAAS,EAAE3B,KAAK,EAAEY,GAAG,CAAC;EACvCtB,WAAW,CAACqC,SAAS,EAAE3B,KAAK,EAAE4B,SAAS,CAAC;EACxCrC,aAAa,CAACoC,SAAS,EAAE3B,KAAK,EAAE4B,SAAS,CAAC;EAE1C,IAAI5B,KAAK,CAAC6B,KAAK,EAAE;IACfF,SAAS,CAACE,KAAK,GAAG7B,KAAK,CAAC6B,KAAK;EAC/B;EAEA,IAAID,SAAS,CAACE,MAAM,EAAE;IACpBH,SAAS,CAACI,QAAQ,GAAGH,SAAS;EAChC;EAEA,MAAMI,MAAM,GAAGxC,gBAAgB,CAACQ,KAAK,CAAC;EACtC,IAAIgC,MAAM,KAAK,IAAI,EAAE;IACnBL,SAAS,CAACK,MAAM,GAAGA,MAAM;EAC3B;EAEA,IAAIlB,OAAO,IAAI,IAAI,EAAE;IACnBa,SAAS,CAACb,OAAO,GAAGpB,cAAc,CAACoB,OAAO,CAAC;EAC7C;EAEA,IAAII,OAAO,IAAI,IAAI,EAAE;IACnBS,SAAS,CAACT,OAAO,GAAGA,OAAO,KAAK,MAAM,GAAG,MAAM,GAAGV,SAAS;EAC7D;EAEA,IAAIO,QAAQ,EAAE;IACZY,SAAS,CAACZ,QAAQ,GAAGA,QAAQ;EAC/B;EAEA,IAAIM,WAAW,EAAE;IACfM,SAAS,CAACN,WAAW,GAAGf,SAAS,CAACe,WAAW,CAAC;EAChD;EACA,IAAIC,SAAS,EAAE;IACbK,SAAS,CAACL,SAAS,GAAGhB,SAAS,CAACgB,SAAS,CAAC;EAC5C;EACA,IAAIC,SAAS,EAAE;IACbI,SAAS,CAACJ,SAAS,GAAGjB,SAAS,CAACiB,SAAS,CAAC;EAC5C;EAEA,IAAIV,EAAE,EAAE;IACNc,SAAS,CAACM,IAAI,GAAGC,MAAM,CAACrB,EAAE,CAAC;EAC7B;EAEA,IAAIW,MAAM,EAAE;IACVG,SAAS,CAACH,MAAM,GAAGA,MAAM;EAC3B;EAEA,IAAIC,kBAAkB,EAAE;IACtBE,SAAS,CAACF,kBAAkB,GAAGA,kBAAkB;EACnD;EAEA,IAAIC,UAAU,EAAE;IACdC,SAAS,CAACD,UAAU,GAAGA,UAAU;EACnC;EAEA,IAAIT,QAAQ,EAAE;IACZU,SAAS,CAACV,QAAQ,GAAGrB,SAAS,CAACqB,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;EACxD;EACA,IAAID,QAAQ,EAAE;IACZ,MAAMP,OAAO,GAAGO,QAAQ,CAACN,KAAK,CAACf,SAAS,CAAC;IACzC,IAAIc,OAAO,EAAE;MACXkB,SAAS,CAACX,QAAQ,GAAGP,OAAO,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM;MACL0B,OAAO,CAACC,IAAI,CACV,qEAAqE,GACnEpB,QAAQ,GACR,GACJ,CAAC;IACH;EACF;EAEA,IAAIG,IAAI,EAAE;IACR,MAAMV,OAAO,GAAGU,IAAI,CAACT,KAAK,CAACf,SAAS,CAAC;IAErC,IAAIc,OAAO,EAAE;MACXkB,SAAS,CAACR,IAAI,GAAGV,OAAO,CAAC,CAAC,CAAC;IAC7B,CAAC,MAAM;MACL0B,OAAO,CAACC,IAAI,CACV,6DAA6D,GAC3DjB,IAAI,GACJ,GACJ,CAAC;IACH;EACF;EAEA,IAAIC,MAAM,EAAE;IACV,MAAMX,OAAO,GAAGW,MAAM,CAACV,KAAK,CAACf,SAAS,CAAC;IAEvC,IAAIc,OAAO,EAAE;MACXkB,SAAS,CAACP,MAAM,GAAGX,OAAO,CAAC,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL0B,OAAO,CAACC,IAAI,CACV,iEAAiE,GAC/DhB,MAAM,GACN,GACJ,CAAC;IACH;EACF;EAEA,OAAOO,SAAS;AAClB;AAEA,OAAO,SAASU,OAAOA,CACrBC,QAAgB,EAChBtC,KAAwC,EACxC;EACA,OAAOW,YAAY,CAACZ,cAAc,CAACC,KAAK,CAAC,EAAEsC,QAAQ,CAAC;AACtD;AAEA,OAAO,SAASC,SAASA,CACvBD,QAAgB,EAChBtC,KAAwC,EACxC;EACA,OAAOW,YAAY,CAAC;IAAE,GAAGZ,cAAc,CAACC,KAAK,CAAC;IAAEwC,CAAC,EAAE,IAAI;IAAEC,CAAC,EAAE;EAAK,CAAC,EAAEH,QAAQ,CAAC;AAC/E", "ignoreList": []}