import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

import { useTranslation } from '../../src/i18n';
import { Button, Input } from '../../src/components/ui';
import { TunaWorkColors, Spacing, FontSizes, BorderRadius } from '../../src/constants';

export default function RegisterScreen() {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    userType: 'freelancer',
    acceptTerms: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateStep1 = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = t('errors.nameRequired');
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = t('errors.nameRequired');
    }

    if (!formData.email) {
      newErrors.email = t('errors.emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('errors.emailInvalid');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.password) {
      newErrors.password = t('errors.passwordRequired');
    } else if (formData.password.length < 8) {
      newErrors.password = t('errors.passwordTooShort');
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = t('errors.passwordMismatch');
    }

    if (!formData.acceptTerms) {
      newErrors.acceptTerms = t('errors.termsRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (currentStep === 1 && validateStep1()) {
      setCurrentStep(2);
    }
  };

  const handleRegister = async () => {
    if (!validateStep2()) return;

    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real app, you would make an API call here
      console.log('Register attempt:', formData);
      
      Alert.alert(
        'Inscription réussie',
        'Votre compte a été créé avec succès !',
        [
          {
            text: 'OK',
            onPress: () => router.replace('/(tabs)'),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Erreur', 'Une erreur est survenue lors de l\'inscription');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const renderStep1 = () => (
    <View style={styles.form}>
      <Input
        label={t('auth.firstName')}
        placeholder="Votre prénom"
        value={formData.firstName}
        onChangeText={(value) => handleInputChange('firstName', value)}
        error={errors.firstName}
        leftIcon="person"
        autoCapitalize="words"
      />

      <Input
        label={t('auth.lastName')}
        placeholder="Votre nom"
        value={formData.lastName}
        onChangeText={(value) => handleInputChange('lastName', value)}
        error={errors.lastName}
        leftIcon="person"
        autoCapitalize="words"
      />

      <Input
        label={t('auth.email')}
        placeholder="<EMAIL>"
        value={formData.email}
        onChangeText={(value) => handleInputChange('email', value)}
        error={errors.email}
        leftIcon="mail"
        keyboardType="email-address"
        autoCapitalize="none"
        autoComplete="email"
      />

      <Button
        title="Suivant"
        onPress={handleNext}
        fullWidth
        style={styles.nextButton}
        rightIcon={<Ionicons name="arrow-forward" size={20} color="#FFFFFF" />}
      />
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.form}>
      {/* User Type Selection */}
      <View style={styles.userTypeContainer}>
        <Text style={styles.userTypeLabel}>{t('auth.userType')}</Text>
        <View style={styles.userTypeButtons}>
          <TouchableOpacity
            style={[
              styles.userTypeButton,
              formData.userType === 'freelancer' && styles.activeUserTypeButton
            ]}
            onPress={() => handleInputChange('userType', 'freelancer')}
          >
            <Ionicons 
              name="briefcase" 
              size={24} 
              color={formData.userType === 'freelancer' ? '#FFFFFF' : TunaWorkColors.secondary[600]} 
            />
            <Text style={[
              styles.userTypeText,
              formData.userType === 'freelancer' && styles.activeUserTypeText
            ]}>
              {t('auth.freelancer')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.userTypeButton,
              formData.userType === 'client' && styles.activeUserTypeButton
            ]}
            onPress={() => handleInputChange('userType', 'client')}
          >
            <Ionicons 
              name="business" 
              size={24} 
              color={formData.userType === 'client' ? '#FFFFFF' : TunaWorkColors.secondary[600]} 
            />
            <Text style={[
              styles.userTypeText,
              formData.userType === 'client' && styles.activeUserTypeText
            ]}>
              {t('auth.client')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <Input
        label={t('auth.password')}
        placeholder="Votre mot de passe"
        value={formData.password}
        onChangeText={(value) => handleInputChange('password', value)}
        error={errors.password}
        leftIcon="lock-closed"
        secureTextEntry
      />

      <Input
        label={t('auth.confirmPassword')}
        placeholder="Confirmez votre mot de passe"
        value={formData.confirmPassword}
        onChangeText={(value) => handleInputChange('confirmPassword', value)}
        error={errors.confirmPassword}
        leftIcon="lock-closed"
        secureTextEntry
      />

      {/* Terms Checkbox */}
      <TouchableOpacity
        style={styles.checkboxContainer}
        onPress={() => handleInputChange('acceptTerms', !formData.acceptTerms)}
      >
        <View style={[styles.checkbox, formData.acceptTerms && styles.checkedCheckbox]}>
          {formData.acceptTerms && (
            <Ionicons name="checkmark" size={16} color="#FFFFFF" />
          )}
        </View>
        <Text style={styles.checkboxText}>
          {t('auth.acceptTerms')}
        </Text>
      </TouchableOpacity>
      {errors.acceptTerms && (
        <Text style={styles.errorText}>{errors.acceptTerms}</Text>
      )}

      <View style={styles.buttonContainer}>
        <Button
          title="Retour"
          variant="outline"
          onPress={() => setCurrentStep(1)}
          style={styles.backButton}
        />
        <Button
          title={t('auth.createAccount')}
          onPress={handleRegister}
          loading={isLoading}
          style={styles.registerButton}
        />
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={() => router.back()} style={styles.backButtonHeader}>
              <Ionicons name="arrow-back" size={24} color={TunaWorkColors.secondary[900]} />
            </TouchableOpacity>
            
            {/* Progress Indicator */}
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: `${(currentStep / 2) * 100}%` }]} />
              </View>
              <Text style={styles.progressText}>Étape {currentStep} sur 2</Text>
            </View>
          </View>

          {/* Logo and Title */}
          <View style={styles.logoContainer}>
            <LinearGradient
              colors={TunaWorkColors.gradient.primary}
              style={styles.logo}
            >
              <Text style={styles.logoText}>T</Text>
            </LinearGradient>
            <Text style={styles.title}>{t('auth.createAccount')}</Text>
            <Text style={styles.subtitle}>
              {currentStep === 1 
                ? 'Commençons par vos informations de base'
                : 'Finalisez votre inscription'
              }
            </Text>
          </View>

          {/* Form Steps */}
          {currentStep === 1 ? renderStep1() : renderStep2()}

          {/* Login Link */}
          <View style={styles.loginContainer}>
            <Text style={styles.loginText}>{t('auth.alreadyHaveAccount')}</Text>
            <TouchableOpacity onPress={() => router.push('/auth/login')}>
              <Text style={styles.loginLink}>{t('auth.signIn')}</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: Spacing.md,
  },
  header: {
    marginBottom: Spacing.lg,
  },
  backButtonHeader: {
    padding: Spacing.sm,
    alignSelf: 'flex-start',
    marginBottom: Spacing.md,
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: TunaWorkColors.secondary[200],
    borderRadius: 2,
    marginBottom: Spacing.sm,
  },
  progressFill: {
    height: '100%',
    backgroundColor: TunaWorkColors.primary[500],
    borderRadius: 2,
  },
  progressText: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: Spacing['2xl'],
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.lg,
  },
  logoText: {
    fontSize: FontSizes['3xl'],
    fontWeight: '700',
    color: '#FFFFFF',
  },
  title: {
    fontSize: FontSizes['3xl'],
    fontWeight: '700',
    color: TunaWorkColors.secondary[900],
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[600],
    textAlign: 'center',
  },
  form: {
    marginBottom: Spacing.lg,
  },
  nextButton: {
    marginTop: Spacing.lg,
  },
  userTypeContainer: {
    marginBottom: Spacing.lg,
  },
  userTypeLabel: {
    fontSize: FontSizes.base,
    fontWeight: '500',
    color: TunaWorkColors.secondary[700],
    marginBottom: Spacing.sm,
  },
  userTypeButtons: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  userTypeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.md,
    borderRadius: BorderRadius.lg,
    borderWidth: 2,
    borderColor: TunaWorkColors.secondary[300],
    backgroundColor: '#FFFFFF',
    gap: Spacing.sm,
  },
  activeUserTypeButton: {
    borderColor: TunaWorkColors.primary[500],
    backgroundColor: TunaWorkColors.primary[500],
  },
  userTypeText: {
    fontSize: FontSizes.base,
    fontWeight: '500',
    color: TunaWorkColors.secondary[600],
  },
  activeUserTypeText: {
    color: '#FFFFFF',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: TunaWorkColors.secondary[300],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.sm,
  },
  checkedCheckbox: {
    backgroundColor: TunaWorkColors.primary[500],
    borderColor: TunaWorkColors.primary[500],
  },
  checkboxText: {
    flex: 1,
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[700],
  },
  errorText: {
    fontSize: FontSizes.sm,
    color: '#EF4444',
    marginBottom: Spacing.sm,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: Spacing.sm,
    marginTop: Spacing.lg,
  },
  backButton: {
    flex: 1,
  },
  registerButton: {
    flex: 2,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 'auto',
    paddingTop: Spacing.lg,
  },
  loginText: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[600],
  },
  loginLink: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.primary[500],
    fontWeight: '600',
    marginLeft: Spacing.xs,
  },
});
