{"version": 3, "sources": ["ForceTouchGestureHandler.ts"], "names": ["React", "tagMessage", "PlatformConstants", "createHandler", "baseGestureHandlerProps", "forceTouchGestureHandlerProps", "ForceTouchFallback", "Component", "componentDidMount", "console", "warn", "render", "props", "children", "forceTouchHandlerName", "ForceTouchGestureHandler", "forceTouchAvailable", "name", "allowedProps", "config"], "mappings": ";;AAAA,OAAOA,KAAP,MAAyC,OAAzC;AACA,SAASC,UAAT,QAA2B,UAA3B;AACA,OAAOC,iBAAP,MAA8B,sBAA9B;AACA,OAAOC,aAAP,MAA0B,iBAA1B;AACA,SAEEC,uBAFF,QAGO,wBAHP;AAMA,OAAO,MAAMC,6BAA6B,GAAG,CAC3C,UAD2C,EAE3C,UAF2C,EAG3C,sBAH2C,CAAtC,C,CAMP;;AACA,MAAMC,kBAAN,SAAiCN,KAAK,CAACO,SAAvC,CAA6E;AAE3EC,EAAAA,iBAAiB,GAAG;AAClBC,IAAAA,OAAO,CAACC,IAAR,CACET,UAAU,CACR,8NADQ,CADZ;AAKD;;AACDU,EAAAA,MAAM,GAAG;AACP,WAAO,KAAKC,KAAL,CAAWC,QAAlB;AACD;;AAX0E;;gBAAvEP,kB,yBACyB,K;;AAgD/B,OAAO,MAAMQ,qBAAqB,GAAG,0BAA9B;AAEP;AACA;AACA;AACA;;AACA,OAAO,MAAMC,wBAAwB,GAAGb,iBAAiB,SAAjB,IAAAA,iBAAiB,WAAjB,IAAAA,iBAAiB,CAAEc,mBAAnB,GACpCb,aAAa,CAGX;AACAc,EAAAA,IAAI,EAAEH,qBADN;AAEAI,EAAAA,YAAY,EAAE,CACZ,GAAGd,uBADS,EAEZ,GAAGC,6BAFS,CAFd;AAMAc,EAAAA,MAAM,EAAE;AANR,CAHW,CADuB,GAYpCb,kBAZG;AAcNS,wBAAD,CAAuDC,mBAAvD,GACE,CAAAd,iBAAiB,SAAjB,IAAAA,iBAAiB,WAAjB,YAAAA,iBAAiB,CAAEc,mBAAnB,KAA0C,KAD5C", "sourcesContent": ["import React, { PropsWithChildren } from 'react';\nimport { tagMessage } from '../utils';\nimport PlatformConstants from '../PlatformConstants';\nimport createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\nimport type { ForceTouchGestureHandlerEventPayload } from './GestureHandlerEventPayload';\n\nexport const forceTouchGestureHandlerProps = [\n  'minForce',\n  'maxForce',\n  'feedbackOnActivation',\n] as const;\n\n// implicit `children` prop has been removed in @types/react^18.0.0\nclass ForceTouchFallback extends React.Component<PropsWithChildren<unknown>> {\n  static forceTouchAvailable = false;\n  componentDidMount() {\n    console.warn(\n      tagMessage(\n        'ForceTouchGestureHandler is not available on this platform. Please use ForceTouchGestureHandler.forceTouchAvailable to conditionally render other components that would provide a fallback behavior specific to your usecase'\n      )\n    );\n  }\n  render() {\n    return this.props.children;\n  }\n}\n\nexport interface ForceTouchGestureConfig {\n  /**\n   *\n   * A minimal pressure that is required before handler can activate. Should be a\n   * value from range `[0.0, 1.0]`. Default is `0.2`.\n   */\n  minForce?: number;\n\n  /**\n   * A maximal pressure that could be applied for handler. If the pressure is\n   * greater, handler fails. Should be a value from range `[0.0, 1.0]`.\n   */\n  maxForce?: number;\n\n  /**\n   * Boolean value defining if haptic feedback has to be performed on\n   * activation.\n   */\n  feedbackOnActivation?: boolean;\n}\n\n/**\n * @deprecated ForceTouchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.ForceTouch()` instead.\n */\nexport interface ForceTouchGestureHandlerProps\n  extends BaseGestureHandlerProps<ForceTouchGestureHandlerEventPayload>,\n    ForceTouchGestureConfig {}\n\n/**\n * @deprecated ForceTouchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.ForceTouch()` instead.\n */\nexport type ForceTouchGestureHandler = typeof ForceTouchGestureHandler & {\n  forceTouchAvailable: boolean;\n};\n\nexport const forceTouchHandlerName = 'ForceTouchGestureHandler';\n\n/**\n * @deprecated ForceTouchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.ForceTouch()` instead.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const ForceTouchGestureHandler = PlatformConstants?.forceTouchAvailable\n  ? createHandler<\n      ForceTouchGestureHandlerProps,\n      ForceTouchGestureHandlerEventPayload\n    >({\n      name: forceTouchHandlerName,\n      allowedProps: [\n        ...baseGestureHandlerProps,\n        ...forceTouchGestureHandlerProps,\n      ] as const,\n      config: {},\n    })\n  : ForceTouchFallback;\n\n(ForceTouchGestureHandler as ForceTouchGestureHandler).forceTouchAvailable =\n  PlatformConstants?.forceTouchAvailable || false;\n"]}