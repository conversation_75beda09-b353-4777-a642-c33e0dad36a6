{"version": 3, "sources": ["flingGesture.ts"], "names": ["FlingGesture", "BaseGesture", "constructor", "handler<PERSON>ame", "numberOfPointers", "pointers", "config", "direction"], "mappings": ";;;;;;;AAAA;;;;AAIO,MAAMA,YAAN,SAA2BC,oBAA3B,CAAwE;AAG7EC,EAAAA,WAAW,GAAG;AACZ;;AADY,oCAF0C,EAE1C;;AAGZ,SAAKC,WAAL,GAAmB,qBAAnB;AACD;AAED;AACF;AACA;AACA;;;AACEC,EAAAA,gBAAgB,CAACC,QAAD,EAAmB;AACjC,SAAKC,MAAL,CAAYF,gBAAZ,GAA+BC,QAA/B;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACEE,EAAAA,SAAS,CAACA,SAAD,EAAoB;AAC3B,SAAKD,MAAL,CAAYC,SAAZ,GAAwBA,SAAxB;AACA,WAAO,IAAP;AACD;;AA5B4E", "sourcesContent": ["import { BaseGesture, BaseGestureConfig } from './gesture';\nimport { FlingGestureConfig } from '../FlingGestureHandler';\nimport type { FlingGestureHandlerEventPayload } from '../GestureHandlerEventPayload';\n\nexport class FlingGesture extends BaseGesture<FlingGestureHandlerEventPayload> {\n  public config: BaseGestureConfig & FlingGestureConfig = {};\n\n  constructor() {\n    super();\n\n    this.handlerName = 'FlingGestureHandler';\n  }\n\n  /**\n   * Determine exact number of points required to handle the fling gesture.\n   * @param pointers\n   */\n  numberOfPointers(pointers: number) {\n    this.config.numberOfPointers = pointers;\n    return this;\n  }\n\n  /**\n   * Expressed allowed direction of movement.\n   * Expected values are exported as constants in the Directions object.\n   * Arguments can be combined using `|` operator. Default value is set to `MouseButton.LEFT`.\n   * @param direction\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/fling-gesture/#directionvalue-directions\n   */\n  direction(direction: number) {\n    this.config.direction = direction;\n    return this;\n  }\n}\n\nexport type FlingGestureType = InstanceType<typeof FlingGesture>;\n"]}