import React from "react";
import { Dimensions, StyleSheet, Text, View } from "react-native";

import { useTranslation } from "../../i18n";
import { OnboardingSlideProps } from "./types";

const { width, height } = Dimensions.get("window");

export function OnboardingSlide({
  slide,
  isActive,
  onNext,
  onSkip,
  currentIndex,
  totalSlides,
}: OnboardingSlideProps) {
  const { t } = useTranslation();

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const imageScale = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    if (isActive) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(imageScale, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isActive]);

  const getSlideIcon = () => {
    switch (currentIndex) {
      case 0:
        return "people";
      case 1:
        return "rocket";
      case 2:
        return "shield-checkmark";
      default:
        return "star";
    }
  };

  const getGradientColors = () => {
    switch (currentIndex) {
      case 0:
        return TunaWorkColors.gradient.primary;
      case 1:
        return TunaWorkColors.gradient.secondary;
      case 2:
        return TunaWorkColors.gradient.accent;
      default:
        return TunaWorkColors.gradient.primary;
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={getGradientColors()}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      {/* Skip Button */}
      {onSkip && currentIndex < totalSlides - 1 && (
        <Animated.View style={[styles.skipContainer, { opacity: fadeAnim }]}>
          <Button
            title={t("onboarding.skip")}
            variant="ghost"
            onPress={onSkip}
            style={styles.skipButton}
          />
        </Animated.View>
      )}

      {/* Progress Indicators */}
      <Animated.View style={[styles.progressContainer, { opacity: fadeAnim }]}>
        {Array.from({ length: totalSlides }).map((_, index) => (
          <View
            key={index}
            style={[
              styles.progressDot,
              index === currentIndex && styles.activeProgressDot,
            ]}
          />
        ))}
      </Animated.View>

      {/* Content */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* Icon/Image Container */}
        <Animated.View
          style={[
            styles.imageContainer,
            { transform: [{ scale: imageScale }] },
          ]}
        >
          <View style={styles.iconBackground}>
            <Ionicons name={getSlideIcon() as any} size={80} color="#FFFFFF" />
          </View>
        </Animated.View>

        {/* Text Content */}
        <View style={styles.textContainer}>
          {slide.subtitle && (
            <Text style={styles.subtitle}>{slide.subtitle}</Text>
          )}
          <Text style={styles.title}>{slide.title}</Text>
          <Text style={styles.description}>{slide.description}</Text>
        </View>

        {/* Action Button */}
        <View style={styles.buttonContainer}>
          <Button
            title={slide.isLast ? t("onboarding.getStarted") : slide.buttonText}
            onPress={onNext}
            fullWidth
            size="lg"
            rightIcon={
              !slide.isLast ? (
                <Ionicons name="arrow-forward" size={20} color="#FFFFFF" />
              ) : undefined
            }
          />
        </View>

        {/* Swipe Hint */}
        {!slide.isLast && (
          <Text style={styles.swipeHint}>{t("onboarding.swipe_hint")}</Text>
        )}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width,
    height,
  },
  background: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  skipContainer: {
    position: "absolute",
    top: 60,
    right: Spacing.md,
    zIndex: 10,
  },
  skipButton: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: BorderRadius.lg,
  },
  progressContainer: {
    position: "absolute",
    top: 120,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "center",
    gap: Spacing.xs,
    zIndex: 10,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "rgba(255, 255, 255, 0.4)",
  },
  activeProgressDot: {
    backgroundColor: "#FFFFFF",
    width: 24,
  },
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingTop: 180,
    paddingBottom: 100,
  },
  imageContainer: {
    marginBottom: Spacing["2xl"],
  },
  iconBackground: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 16,
  },
  textContainer: {
    alignItems: "center",
    marginBottom: Spacing["2xl"],
  },
  subtitle: {
    fontSize: FontSizes.lg,
    color: "rgba(255, 255, 255, 0.8)",
    textAlign: "center",
    marginBottom: Spacing.sm,
    fontWeight: "500",
  },
  title: {
    fontSize: FontSizes["3xl"],
    fontWeight: "700",
    color: "#FFFFFF",
    textAlign: "center",
    marginBottom: Spacing.lg,
    lineHeight: FontSizes["3xl"] * 1.2,
  },
  description: {
    fontSize: FontSizes.lg,
    color: "rgba(255, 255, 255, 0.9)",
    textAlign: "center",
    lineHeight: FontSizes.lg * 1.4,
    paddingHorizontal: Spacing.sm,
  },
  buttonContainer: {
    width: "100%",
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
  },
  swipeHint: {
    fontSize: FontSizes.sm,
    color: "rgba(255, 255, 255, 0.6)",
    textAlign: "center",
  },
});
