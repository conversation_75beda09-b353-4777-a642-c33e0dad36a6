@echo off
echo ========================================
echo    TunaWork - Generation APK Android
echo ========================================
echo.

echo Etape 1: Installation d'EAS CLI...
npm install -g @expo/eas-cli
if %errorlevel% neq 0 (
    echo Erreur lors de l'installation d'EAS CLI
    pause
    exit /b 1
)

echo.
echo Etape 2: Connexion a Expo (si necessaire)...
echo Veuillez vous connecter a votre compte Expo si demande
eas login

echo.
echo Etape 3: Configuration du projet...
eas build:configure

echo.
echo Etape 4: Generation de l'APK Android...
eas build --platform android --profile preview

echo.
echo ========================================
echo    Generation terminee !
echo ========================================
echo L'APK sera disponible dans votre dashboard Expo
echo Visitez: https://expo.dev/accounts/[votre-username]/projects/tunawork-mobile/builds
echo.
pause
