{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_extractProps", "_extractTransform", "_interopRequireDefault", "_extractText", "_util", "_Shape", "_TSpanNativeComponent", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "TSpan", "<PERSON><PERSON><PERSON>", "displayName", "setNativeProps", "props", "matrix", "extractTransform", "prop", "propsAndStyles", "assign", "pickNotNil", "extractText", "root", "render", "extractProps", "x", "y", "ref", "refMethod", "createElement", "exports", "setTSpan"], "sourceRoot": "../../../src", "sources": ["elements/TSpan.tsx"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,iBAAA,GAAAC,sBAAA,CAAAH,OAAA;AAEA,IAAAI,YAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAH,sBAAA,CAAAH,OAAA;AAQA,IAAAO,qBAAA,GAAAJ,sBAAA,CAAAH,OAAA;AAAwD,SAAAG,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAYzC,MAAMW,KAAK,SAASC,cAAK,CAAa;EACnD,OAAOC,WAAW,GAAG,OAAO;EAE5BC,cAAc,GACZC,KAGC,IACE;IACH,MAAMC,MAAM,GAAG,CAACD,KAAK,CAACC,MAAM,IAAI,IAAAC,yBAAgB,EAACF,KAAK,CAAC;IACvD,IAAIC,MAAM,EAAE;MACVD,KAAK,CAACC,MAAM,GAAGA,MAAM;IACvB;IACA,MAAME,IAAI,GAAG,IAAAC,4BAAc,EAACJ,KAAK,CAAC;IAClCZ,MAAM,CAACiB,MAAM,CAACF,IAAI,EAAE,IAAAG,gBAAU,EAAC,IAAAC,oBAAW,EAACJ,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;IACzD,IAAI,CAACK,IAAI,IAAI,IAAI,CAACA,IAAI,CAACT,cAAc,CAACI,IAAI,CAAC;EAC7C,CAAC;EAEDM,MAAMA,CAAA,EAAG;IACP,MAAMN,IAAI,GAAG,IAAAC,4BAAc,EAAC,IAAI,CAACJ,KAAK,CAAC;IACvC,MAAMA,KAAK,GAAG,IAAAU,qBAAY,EACxB;MACE,GAAGP,IAAI;MACPQ,CAAC,EAAE,IAAI;MACPC,CAAC,EAAE;IACL,CAAC,EACD,IACF,CAAC;IACDxB,MAAM,CAACiB,MAAM,CAACL,KAAK,EAAE,IAAAO,oBAAW,EAACJ,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9CH,KAAK,CAACa,GAAG,GAAG,IAAI,CAACC,SAAiD;IAClE,oBAAOhD,KAAA,CAAAiD,aAAA,CAACxC,qBAAA,CAAAG,OAAU,EAAKsB,KAAQ,CAAC;EAClC;AACF;AAACgB,OAAA,CAAAtC,OAAA,GAAAkB,KAAA;AAED,IAAAqB,qBAAQ,EAACrB,KAAK,CAAC", "ignoreList": []}