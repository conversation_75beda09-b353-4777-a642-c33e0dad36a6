{"version": 3, "sources": ["RotationGestureDetector.ts"], "names": ["RotationGestureDetector", "constructor", "callbacks", "NaN", "onRotationBegin", "onRotation", "onRotationEnd", "updateCurrent", "event", "tracker", "previousTime", "currentTime", "time", "firstPointerID", "secondPointerID", "keyPointers", "firstPointerCoords", "getLastAbsoluteCoords", "secondPointerCoords", "vectorX", "x", "vectorY", "y", "_anchorX", "_anchorY", "angle", "Math", "atan2", "_rotation", "Number", "isNaN", "previousAngle", "rotation", "PI", "finish", "isInProgress", "set<PERSON>eyPointers", "pointerIDs", "trackedPointers", "keys", "next", "value", "onTouchEvent", "eventType", "EventTypes", "DOWN", "ADDITIONAL_POINTER_DOWN", "MOVE", "ADDITIONAL_POINTER_UP", "indexOf", "pointerId", "UP", "reset", "anchorX", "anchorY", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;AAAA;;;;AASe,MAAMA,uBAAN,CAEf;AAkBEC,EAAAA,WAAW,CAACC,SAAD,EAAqC;AAAA;;AAAA;;AAAA;;AAAA,yCAb1B,CAa0B;;AAAA,0CAZzB,CAYyB;;AAAA,2CAVxB,CAUwB;;AAAA,uCAT5B,CAS4B;;AAAA,sCAP7B,CAO6B;;AAAA,sCAN7B,CAM6B;;AAAA,0CAJzB,KAIyB;;AAAA,yCAFhB,CAACC,GAAD,EAAMA,GAAN,CAEgB;;AAC9C,SAAKC,eAAL,GAAuBF,SAAS,CAACE,eAAjC;AACA,SAAKC,UAAL,GAAkBH,SAAS,CAACG,UAA5B;AACA,SAAKC,aAAL,GAAqBJ,SAAS,CAACI,aAA/B;AACD;;AAEOC,EAAAA,aAAa,CAACC,KAAD,EAAsBC,OAAtB,EAAqD;AACxE,SAAKC,YAAL,GAAoB,KAAKC,WAAzB;AACA,SAAKA,WAAL,GAAmBH,KAAK,CAACI,IAAzB;AAEA,UAAM,CAACC,cAAD,EAAiBC,eAAjB,IAAoC,KAAKC,WAA/C;AAEA,UAAMC,kBAAkB,GAAGP,OAAO,CAACQ,qBAAR,CAA8BJ,cAA9B,CAA3B;AACA,UAAMK,mBAAmB,GAAGT,OAAO,CAACQ,qBAAR,CAA8BH,eAA9B,CAA5B;AAEA,UAAMK,OAAe,GAAGD,mBAAmB,CAACE,CAApB,GAAwBJ,kBAAkB,CAACI,CAAnE;AACA,UAAMC,OAAe,GAAGH,mBAAmB,CAACI,CAApB,GAAwBN,kBAAkB,CAACM,CAAnE;AAEA,SAAKC,QAAL,GAAgB,CAACP,kBAAkB,CAACI,CAAnB,GAAuBF,mBAAmB,CAACE,CAA5C,IAAiD,CAAjE;AACA,SAAKI,QAAL,GAAgB,CAACR,kBAAkB,CAACM,CAAnB,GAAuBJ,mBAAmB,CAACI,CAA5C,IAAiD,CAAjE,CAbwE,CAexE;;AACA,UAAMG,KAAa,GAAG,CAACC,IAAI,CAACC,KAAL,CAAWN,OAAX,EAAoBF,OAApB,CAAvB;AAEA,SAAKS,SAAL,GAAiBC,MAAM,CAACC,KAAP,CAAa,KAAKC,aAAlB,IACb,CADa,GAEb,KAAKA,aAAL,GAAqBN,KAFzB;AAIA,SAAKM,aAAL,GAAqBN,KAArB;;AAEA,QAAI,KAAKO,QAAL,GAAgBN,IAAI,CAACO,EAAzB,EAA6B;AAC3B,WAAKL,SAAL,IAAkBF,IAAI,CAACO,EAAvB;AACD,KAFD,MAEO,IAAI,KAAKD,QAAL,GAAgB,CAACN,IAAI,CAACO,EAA1B,EAA8B;AACnC,WAAKL,SAAL,IAAkBF,IAAI,CAACO,EAAvB;AACD;;AAED,QAAI,KAAKD,QAAL,GAAgBN,IAAI,CAACO,EAAL,GAAU,CAA9B,EAAiC;AAC/B,WAAKL,SAAL,IAAkBF,IAAI,CAACO,EAAvB;AACD,KAFD,MAEO,IAAI,KAAKD,QAAL,GAAgB,CAACN,IAAI,CAACO,EAAN,GAAW,CAA/B,EAAkC;AACvC,WAAKL,SAAL,IAAkBF,IAAI,CAACO,EAAvB;AACD;AACF;;AAEOC,EAAAA,MAAM,GAAS;AACrB,QAAI,CAAC,KAAKC,YAAV,EAAwB;AACtB;AACD;;AAED,SAAKA,YAAL,GAAoB,KAApB;AACA,SAAKpB,WAAL,GAAmB,CAACZ,GAAD,EAAMA,GAAN,CAAnB;AACA,SAAKG,aAAL,CAAmB,IAAnB;AACD;;AAEO8B,EAAAA,cAAc,CAAC3B,OAAD,EAAgC;AACpD,QAAI,KAAKM,WAAL,CAAiB,CAAjB,KAAuB,KAAKA,WAAL,CAAiB,CAAjB,CAA3B,EAAgD;AAC9C;AACD;;AAED,UAAMsB,UAAoC,GAAG5B,OAAO,CAAC6B,eAAR,CAAwBC,IAAxB,EAA7C;AAEA,SAAKxB,WAAL,CAAiB,CAAjB,IAAsBsB,UAAU,CAACG,IAAX,GAAkBC,KAAxC;AACA,SAAK1B,WAAL,CAAiB,CAAjB,IAAsBsB,UAAU,CAACG,IAAX,GAAkBC,KAAxC;AACD;;AAEMC,EAAAA,YAAY,CAAClC,KAAD,EAAsBC,OAAtB,EAAwD;AACzE,YAAQD,KAAK,CAACmC,SAAd;AACE,WAAKC,uBAAWC,IAAhB;AACE,aAAKV,YAAL,GAAoB,KAApB;AACA;;AAEF,WAAKS,uBAAWE,uBAAhB;AACE,YAAI,KAAKX,YAAT,EAAuB;AACrB;AACD;;AACD,aAAKA,YAAL,GAAoB,IAApB;AAEA,aAAKzB,YAAL,GAAoBF,KAAK,CAACI,IAA1B;AACA,aAAKmB,aAAL,GAAqB5B,GAArB;AAEA,aAAKiC,cAAL,CAAoB3B,OAApB;AAEA,aAAKF,aAAL,CAAmBC,KAAnB,EAA0BC,OAA1B;AACA,aAAKL,eAAL,CAAqB,IAArB;AACA;;AAEF,WAAKwC,uBAAWG,IAAhB;AACE,YAAI,CAAC,KAAKZ,YAAV,EAAwB;AACtB;AACD;;AAED,aAAK5B,aAAL,CAAmBC,KAAnB,EAA0BC,OAA1B;AACA,aAAKJ,UAAL,CAAgB,IAAhB;AAEA;;AAEF,WAAKuC,uBAAWI,qBAAhB;AACE,YAAI,CAAC,KAAKb,YAAV,EAAwB;AACtB;AACD;;AAED,YAAI,KAAKpB,WAAL,CAAiBkC,OAAjB,CAAyBzC,KAAK,CAAC0C,SAA/B,KAA6C,CAAjD,EAAoD;AAClD,eAAKhB,MAAL;AACD;;AAED;;AAEF,WAAKU,uBAAWO,EAAhB;AACE,YAAI,KAAKhB,YAAT,EAAuB;AACrB,eAAKD,MAAL;AACD;;AACD;AA7CJ;;AAgDA,WAAO,IAAP;AACD;;AAEMkB,EAAAA,KAAK,GAAS;AACnB,SAAKrC,WAAL,GAAmB,CAACZ,GAAD,EAAMA,GAAN,CAAnB;AACA,SAAKgC,YAAL,GAAoB,KAApB;AACD;;AAEiB,MAAPkB,OAAO,GAAG;AACnB,WAAO,KAAK9B,QAAZ;AACD;;AAEiB,MAAP+B,OAAO,GAAG;AACnB,WAAO,KAAK9B,QAAZ;AACD;;AAEkB,MAARQ,QAAQ,GAAG;AACpB,WAAO,KAAKJ,SAAZ;AACD;;AAEmB,MAAT2B,SAAS,GAAG;AACrB,WAAO,KAAK5C,WAAL,GAAmB,KAAKD,YAA/B;AACD;;AAzJH", "sourcesContent": ["import { AdaptedEvent, EventTypes } from '../interfaces';\nimport PointerTracker from '../tools/PointerTracker';\n\nexport interface RotationGestureListener {\n  onRotationBegin: (detector: RotationGestureDetector) => boolean;\n  onRotation: (detector: RotationGestureDetector) => boolean;\n  onRotationEnd: (detector: RotationGestureDetector) => void;\n}\n\nexport default class RotationGestureDetector\n  implements RotationGestureListener\n{\n  onRotationBegin: (detector: RotationGestureDetector) => boolean;\n  onRotation: (detector: RotationGestureDetector) => boolean;\n  onRotationEnd: (detector: RotationGestureDetector) => void;\n\n  private currentTime = 0;\n  private previousTime = 0;\n\n  private previousAngle = 0;\n  private _rotation = 0;\n\n  private _anchorX = 0;\n  private _anchorY = 0;\n\n  private isInProgress = false;\n\n  private keyPointers: number[] = [NaN, NaN];\n\n  constructor(callbacks: RotationGestureListener) {\n    this.onRotationBegin = callbacks.onRotationBegin;\n    this.onRotation = callbacks.onRotation;\n    this.onRotationEnd = callbacks.onRotationEnd;\n  }\n\n  private updateCurrent(event: AdaptedEvent, tracker: PointerTracker): void {\n    this.previousTime = this.currentTime;\n    this.currentTime = event.time;\n\n    const [firstPointerID, secondPointerID] = this.keyPointers;\n\n    const firstPointerCoords = tracker.getLastAbsoluteCoords(firstPointerID);\n    const secondPointerCoords = tracker.getLastAbsoluteCoords(secondPointerID);\n\n    const vectorX: number = secondPointerCoords.x - firstPointerCoords.x;\n    const vectorY: number = secondPointerCoords.y - firstPointerCoords.y;\n\n    this._anchorX = (firstPointerCoords.x + secondPointerCoords.x) / 2;\n    this._anchorY = (firstPointerCoords.y + secondPointerCoords.y) / 2;\n\n    // Angle diff should be positive when rotating in clockwise direction\n    const angle: number = -Math.atan2(vectorY, vectorX);\n\n    this._rotation = Number.isNaN(this.previousAngle)\n      ? 0\n      : this.previousAngle - angle;\n\n    this.previousAngle = angle;\n\n    if (this.rotation > Math.PI) {\n      this._rotation -= Math.PI;\n    } else if (this.rotation < -Math.PI) {\n      this._rotation += Math.PI;\n    }\n\n    if (this.rotation > Math.PI / 2) {\n      this._rotation -= Math.PI;\n    } else if (this.rotation < -Math.PI / 2) {\n      this._rotation += Math.PI;\n    }\n  }\n\n  private finish(): void {\n    if (!this.isInProgress) {\n      return;\n    }\n\n    this.isInProgress = false;\n    this.keyPointers = [NaN, NaN];\n    this.onRotationEnd(this);\n  }\n\n  private setKeyPointers(tracker: PointerTracker): void {\n    if (this.keyPointers[0] && this.keyPointers[1]) {\n      return;\n    }\n\n    const pointerIDs: IterableIterator<number> = tracker.trackedPointers.keys();\n\n    this.keyPointers[0] = pointerIDs.next().value as number;\n    this.keyPointers[1] = pointerIDs.next().value as number;\n  }\n\n  public onTouchEvent(event: AdaptedEvent, tracker: PointerTracker): boolean {\n    switch (event.eventType) {\n      case EventTypes.DOWN:\n        this.isInProgress = false;\n        break;\n\n      case EventTypes.ADDITIONAL_POINTER_DOWN:\n        if (this.isInProgress) {\n          break;\n        }\n        this.isInProgress = true;\n\n        this.previousTime = event.time;\n        this.previousAngle = NaN;\n\n        this.setKeyPointers(tracker);\n\n        this.updateCurrent(event, tracker);\n        this.onRotationBegin(this);\n        break;\n\n      case EventTypes.MOVE:\n        if (!this.isInProgress) {\n          break;\n        }\n\n        this.updateCurrent(event, tracker);\n        this.onRotation(this);\n\n        break;\n\n      case EventTypes.ADDITIONAL_POINTER_UP:\n        if (!this.isInProgress) {\n          break;\n        }\n\n        if (this.keyPointers.indexOf(event.pointerId) >= 0) {\n          this.finish();\n        }\n\n        break;\n\n      case EventTypes.UP:\n        if (this.isInProgress) {\n          this.finish();\n        }\n        break;\n    }\n\n    return true;\n  }\n\n  public reset(): void {\n    this.keyPointers = [NaN, NaN];\n    this.isInProgress = false;\n  }\n\n  public get anchorX() {\n    return this._anchorX;\n  }\n\n  public get anchorY() {\n    return this._anchorY;\n  }\n\n  public get rotation() {\n    return this._rotation;\n  }\n\n  public get timeDelta() {\n    return this.currentTime + this.previousTime;\n  }\n}\n"]}