/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/../src/hooks/useOnboarding`; params?: Router.UnknownInputParams; } | { pathname: `/../src/hooks/index`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/onboarding/SplashScreen`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/onboarding/types`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/onboarding/OnboardingSlide`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/onboarding/OnboardingScreen`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/onboarding/OnboardingFlow`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/onboarding/index`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/index`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/feed` | `/feed`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/professionals` | `/professionals`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/register`; params?: Router.UnknownInputParams; } | { pathname: `/freelancer/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/hooks/useOnboarding`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/hooks/index`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/components/onboarding/SplashScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/components/onboarding/types`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/components/onboarding/OnboardingSlide`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/components/onboarding/OnboardingScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/components/onboarding/OnboardingFlow`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/components/onboarding/index`; params?: Router.UnknownOutputParams; } | { pathname: `/../src/components/index`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/feed` | `/feed`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/professionals` | `/professionals`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/login`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/register`; params?: Router.UnknownOutputParams; } | { pathname: `/freelancer/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/../src/hooks/useOnboarding${`?${string}` | `#${string}` | ''}` | `/../src/hooks/index${`?${string}` | `#${string}` | ''}` | `/../src/components/onboarding/SplashScreen${`?${string}` | `#${string}` | ''}` | `/../src/components/onboarding/types${`?${string}` | `#${string}` | ''}` | `/../src/components/onboarding/OnboardingSlide${`?${string}` | `#${string}` | ''}` | `/../src/components/onboarding/OnboardingScreen${`?${string}` | `#${string}` | ''}` | `/../src/components/onboarding/OnboardingFlow${`?${string}` | `#${string}` | ''}` | `/../src/components/onboarding/index${`?${string}` | `#${string}` | ''}` | `/../src/components/index${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/feed${`?${string}` | `#${string}` | ''}` | `/feed${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/professionals${`?${string}` | `#${string}` | ''}` | `/professionals${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `/auth/login${`?${string}` | `#${string}` | ''}` | `/auth/register${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/../src/hooks/useOnboarding`; params?: Router.UnknownInputParams; } | { pathname: `/../src/hooks/index`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/onboarding/SplashScreen`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/onboarding/types`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/onboarding/OnboardingSlide`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/onboarding/OnboardingScreen`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/onboarding/OnboardingFlow`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/onboarding/index`; params?: Router.UnknownInputParams; } | { pathname: `/../src/components/index`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/feed` | `/feed`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/professionals` | `/professionals`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/register`; params?: Router.UnknownInputParams; } | `/freelancer/${Router.SingleRoutePart<T>}` | { pathname: `/freelancer/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
