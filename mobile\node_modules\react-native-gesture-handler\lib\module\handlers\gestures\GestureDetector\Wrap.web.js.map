{"version": 3, "sources": ["Wrap.web.tsx"], "names": ["React", "forwardRef", "tagMessage", "isRNSVGNode", "Wrap", "children", "ref", "child", "Children", "only", "clone", "cloneElement", "props", "display", "e", "Error", "AnimatedWrap"], "mappings": "AAAA,OAAOA,KAAP,IAAgBC,UAAhB,QAAkC,OAAlC;AAEA,SAASC,UAAT,QAA2B,gBAA3B;AACA,SAASC,WAAT,QAA4B,oBAA5B;AAEA,OAAO,MAAMC,IAAI,gBAAGH,UAAU,CAC5B,CAAC;AAAEI,EAAAA;AAAF,CAAD,EAAeC,GAAf,KAAuB;AACrB,MAAI;AACF;AACA,UAAMC,KAAU,GAAGP,KAAK,CAACQ,QAAN,CAAeC,IAAf,CAAoBJ,QAApB,CAAnB;;AAEA,QAAIF,WAAW,CAACI,KAAD,CAAf,EAAwB;AACtB,YAAMG,KAAK,gBAAGV,KAAK,CAACW,YAAN,CACZJ,KADY,EAEZ;AAAED,QAAAA;AAAF,OAFY,EAGZ;AACAC,MAAAA,KAAK,CAACK,KAAN,CAAYP,QAJA,CAAd;AAOA,aAAOK,KAAP;AACD;;AAED,wBACE;AACE,MAAA,GAAG,EAAEJ,GADP;AAEE,MAAA,KAAK,EAAE;AAAEO,QAAAA,OAAO,EAAE;AAAX;AAFT,OAGGN,KAHH,CADF;AAOD,GAtBD,CAsBE,OAAOO,CAAP,EAAU;AACV,UAAM,IAAIC,KAAJ,CACJb,UAAU,CACP,2KADO,CADN,CAAN;AAKD;AACF,CA/B2B,CAAvB,C,CAkCP;AACA;;AACA,OAAO,MAAMc,YAAY,GAAGZ,IAArB", "sourcesContent": ["import React, { forwardRef } from 'react';\nimport type { LegacyRef, PropsWithChildren } from 'react';\nimport { tagMessage } from '../../../utils';\nimport { isRNSVGNode } from '../../../web/utils';\n\nexport const Wrap = forwardRef<HTMLDivElement, PropsWithChildren<{}>>(\n  ({ children }, ref) => {\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const child: any = React.Children.only(children);\n\n      if (isRNSVGNode(child)) {\n        const clone = React.cloneElement(\n          child,\n          { ref },\n          // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n          child.props.children\n        );\n\n        return clone;\n      }\n\n      return (\n        <div\n          ref={ref as LegacyRef<HTMLDivElement>}\n          style={{ display: 'contents' }}>\n          {child}\n        </div>\n      );\n    } catch (e) {\n      throw new Error(\n        tagMessage(\n          `GestureDetector got more than one view as a child. If you want the gesture to work on multiple views, wrap them with a common parent and attach the gesture to that view.`\n        )\n      );\n    }\n  }\n);\n\n// On web we never take a path with <PERSON>animated,\n// therefore we can simply export Wrap\nexport const AnimatedWrap = Wrap;\n"]}