{"version": 3, "sources": ["gesture.ts"], "names": ["getNextHandlerTag", "isRemoteDebuggingEnabled", "CALLBACK_TYPE", "UNDEFINED", "BEGAN", "START", "UPDATE", "CHANGE", "END", "FINALIZE", "TOUCHES_DOWN", "TOUCHES_MOVE", "TOUCHES_UP", "TOUCHES_CANCELLED", "Gesture", "nextGestureId", "BaseGesture", "constructor", "gestureId", "handlerTag", "isWorklet", "handlers", "addDependency", "key", "gesture", "value", "config", "Array", "concat", "with<PERSON>ef", "ref", "callback", "__workletHash", "undefined", "onBegin", "onStart", "onEnd", "onFinalize", "onTouchesDown", "needsPointerData", "onTouchesMove", "onTouchesUp", "onTouchesCancelled", "enabled", "shouldCancelWhenOutside", "hitSlop", "activeCursor", "mouseButton", "runOnJS", "simultaneousWithExternalGesture", "gestures", "requireExternalGestureToFail", "blocksExternalGesture", "withTestId", "id", "testId", "cancelsTouchesInView", "initialize", "current", "toGestureArray", "prepare", "shouldUseReanimated", "includes", "ContinousBaseGesture", "onUpdate", "onChange", "manualActivation"], "mappings": ";;AASA,SAASA,iBAAT,QAAkC,sBAAlC;AAaA,SAASC,wBAAT,QAAyC,aAAzC;AAiEA,OAAO,MAAMC,aAAa,GAAG;AAC3BC,EAAAA,SAAS,EAAE,CADgB;AAE3BC,EAAAA,KAAK,EAAE,CAFoB;AAG3BC,EAAAA,KAAK,EAAE,CAHoB;AAI3BC,EAAAA,MAAM,EAAE,CAJmB;AAK3BC,EAAAA,MAAM,EAAE,CALmB;AAM3BC,EAAAA,GAAG,EAAE,CANsB;AAO3BC,EAAAA,QAAQ,EAAE,CAPiB;AAQ3BC,EAAAA,YAAY,EAAE,CARa;AAS3BC,EAAAA,YAAY,EAAE,CATa;AAU3BC,EAAAA,UAAU,EAAE,CAVe;AAW3BC,EAAAA,iBAAiB,EAAE;AAXQ,CAAtB,C,CAcP;AACA;;AAGA,OAAO,MAAeC,OAAf,CAAuB;AAoB9B,IAAIC,aAAa,GAAG,CAApB;AACA,OAAO,MAAeC,WAAf,SAEGF,OAFH,CAEW;AAWhBG,EAAAA,WAAW,GAAG;AACZ,YADY,CAGZ;AACA;AACA;AACA;AACA;;AAPY,uCAVM,CAAC,CAUP;;AAAA,wCATM,CAAC,CASP;;AAAA,yCARO,EAQP;;AAAA,oCAPqB,EAOrB;;AAAA,sCANqC;AACjDC,MAAAA,SAAS,EAAE,CAAC,CADqC;AAEjDC,MAAAA,UAAU,EAAE,CAAC,CAFoC;AAGjDC,MAAAA,SAAS,EAAE;AAHsC,KAMrC;;AAQZ,SAAKF,SAAL,GAAiBH,aAAa,EAA9B;AACA,SAAKM,QAAL,CAAcH,SAAd,GAA0B,KAAKA,SAA/B;AACD;;AAEOI,EAAAA,aAAa,CACnBC,GADmB,EAEnBC,OAFmB,EAGnB;AACA,UAAMC,KAAK,GAAG,KAAKC,MAAL,CAAYH,GAAZ,CAAd;AACA,SAAKG,MAAL,CAAYH,GAAZ,IAAmBE,KAAK,GACpBE,KAAK,GAAeC,MAApB,CAA2BH,KAA3B,EAAkCD,OAAlC,CADoB,GAEpB,CAACA,OAAD,CAFJ;AAGD;AAED;AACF;AACA;AACA;;;AACEK,EAAAA,OAAO,CAACC,GAAD,EAAuD;AAC5D,SAAKJ,MAAL,CAAYI,GAAZ,GAAkBA,GAAlB;AACA,WAAO,IAAP;AACD,GAxCe,CA0ChB;;;AACUV,EAAAA,SAAS,CAACW,QAAD,EAAqB;AACtC;AACA,WAAOA,QAAQ,CAACC,aAAT,KAA2BC,SAAlC;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEC,EAAAA,OAAO,CAACH,QAAD,EAAoE;AACzE,SAAKV,QAAL,CAAca,OAAd,GAAwBH,QAAxB;AACA,SAAKV,QAAL,CAAcD,SAAd,CAAwBlB,aAAa,CAACE,KAAtC,IAA+C,KAAKgB,SAAL,CAAeW,QAAf,CAA/C;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACEI,EAAAA,OAAO,CAACJ,QAAD,EAAoE;AACzE,SAAKV,QAAL,CAAcc,OAAd,GAAwBJ,QAAxB;AACA,SAAKV,QAAL,CAAcD,SAAd,CAAwBlB,aAAa,CAACG,KAAtC,IAA+C,KAAKe,SAAL,CAAeW,QAAf,CAA/C;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEK,EAAAA,KAAK,CACHL,QADG,EAKH;AACA,SAAKV,QAAL,CAAce,KAAd,GAAsBL,QAAtB,CADA,CAEA;;AACA,SAAKV,QAAL,CAAcD,SAAd,CAAwBlB,aAAa,CAACM,GAAtC,IAA6C,KAAKY,SAAL,CAAeW,QAAf,CAA7C;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACEM,EAAAA,UAAU,CACRN,QADQ,EAKR;AACA,SAAKV,QAAL,CAAcgB,UAAd,GAA2BN,QAA3B,CADA,CAEA;;AACA,SAAKV,QAAL,CAAcD,SAAd,CAAwBlB,aAAa,CAACO,QAAtC,IAAkD,KAAKW,SAAL,CAAeW,QAAf,CAAlD;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACEO,EAAAA,aAAa,CAACP,QAAD,EAAkC;AAC7C,SAAKL,MAAL,CAAYa,gBAAZ,GAA+B,IAA/B;AACA,SAAKlB,QAAL,CAAciB,aAAd,GAA8BP,QAA9B;AACA,SAAKV,QAAL,CAAcD,SAAd,CAAwBlB,aAAa,CAACQ,YAAtC,IACE,KAAKU,SAAL,CAAeW,QAAf,CADF;AAGA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACES,EAAAA,aAAa,CAACT,QAAD,EAAkC;AAC7C,SAAKL,MAAL,CAAYa,gBAAZ,GAA+B,IAA/B;AACA,SAAKlB,QAAL,CAAcmB,aAAd,GAA8BT,QAA9B;AACA,SAAKV,QAAL,CAAcD,SAAd,CAAwBlB,aAAa,CAACS,YAAtC,IACE,KAAKS,SAAL,CAAeW,QAAf,CADF;AAGA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACEU,EAAAA,WAAW,CAACV,QAAD,EAAkC;AAC3C,SAAKL,MAAL,CAAYa,gBAAZ,GAA+B,IAA/B;AACA,SAAKlB,QAAL,CAAcoB,WAAd,GAA4BV,QAA5B;AACA,SAAKV,QAAL,CAAcD,SAAd,CAAwBlB,aAAa,CAACU,UAAtC,IACE,KAAKQ,SAAL,CAAeW,QAAf,CADF;AAGA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACEW,EAAAA,kBAAkB,CAACX,QAAD,EAAkC;AAClD,SAAKL,MAAL,CAAYa,gBAAZ,GAA+B,IAA/B;AACA,SAAKlB,QAAL,CAAcqB,kBAAd,GAAmCX,QAAnC;AACA,SAAKV,QAAL,CAAcD,SAAd,CAAwBlB,aAAa,CAACW,iBAAtC,IACE,KAAKO,SAAL,CAAeW,QAAf,CADF;AAGA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEY,EAAAA,OAAO,CAACA,OAAD,EAAmB;AACxB,SAAKjB,MAAL,CAAYiB,OAAZ,GAAsBA,OAAtB;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEC,EAAAA,uBAAuB,CAACnB,KAAD,EAAiB;AACtC,SAAKC,MAAL,CAAYkB,uBAAZ,GAAsCnB,KAAtC;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEoB,EAAAA,OAAO,CAACA,OAAD,EAAmB;AACxB,SAAKnB,MAAL,CAAYmB,OAAZ,GAAsBA,OAAtB;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEC,EAAAA,YAAY,CAACA,YAAD,EAA6B;AACvC,SAAKpB,MAAL,CAAYoB,YAAZ,GAA2BA,YAA3B;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEC,EAAAA,WAAW,CAACA,WAAD,EAA2B;AACpC,SAAKrB,MAAL,CAAYqB,WAAZ,GAA0BA,WAA1B;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEC,EAAAA,OAAO,CAACA,OAAD,EAAmB;AACxB,SAAKtB,MAAL,CAAYsB,OAAZ,GAAsBA,OAAtB;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEC,EAAAA,+BAA+B,CAAC,GAAGC,QAAJ,EAA6C;AAC1E,SAAK,MAAM1B,OAAX,IAAsB0B,QAAtB,EAAgC;AAC9B,WAAK5B,aAAL,CAAmB,kBAAnB,EAAuCE,OAAvC;AACD;;AACD,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACE2B,EAAAA,4BAA4B,CAAC,GAAGD,QAAJ,EAA6C;AACvE,SAAK,MAAM1B,OAAX,IAAsB0B,QAAtB,EAAgC;AAC9B,WAAK5B,aAAL,CAAmB,eAAnB,EAAoCE,OAApC;AACD;;AACD,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACE4B,EAAAA,qBAAqB,CAAC,GAAGF,QAAJ,EAA6C;AAChE,SAAK,MAAM1B,OAAX,IAAsB0B,QAAtB,EAAgC;AAC9B,WAAK5B,aAAL,CAAmB,gBAAnB,EAAqCE,OAArC;AACD;;AACD,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACE6B,EAAAA,UAAU,CAACC,EAAD,EAAa;AACrB,SAAK5B,MAAL,CAAY6B,MAAZ,GAAqBD,EAArB;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEE,EAAAA,oBAAoB,CAAC/B,KAAD,EAAiB;AACnC,SAAKC,MAAL,CAAY8B,oBAAZ,GAAmC/B,KAAnC;AACA,WAAO,IAAP;AACD;;AAEDgC,EAAAA,UAAU,GAAG;AACX,SAAKtC,UAAL,GAAkBnB,iBAAiB,EAAnC;AAEA,SAAKqB,QAAL,GAAgB,EAAE,GAAG,KAAKA,QAAV;AAAoBF,MAAAA,UAAU,EAAE,KAAKA;AAArC,KAAhB;;AAEA,QAAI,KAAKO,MAAL,CAAYI,GAAhB,EAAqB;AACnB,WAAKJ,MAAL,CAAYI,GAAZ,CAAgB4B,OAAhB,GAA0B,IAA1B;AACD;AACF;;AAEDC,EAAAA,cAAc,GAAkB;AAC9B,WAAO,CAAC,IAAD,CAAP;AACD,GAhSe,CAkShB;;;AACAC,EAAAA,OAAO,GAAG,CAAE;;AAEW,MAAnBC,mBAAmB,GAAY;AACjC;AACA;AACA;AACA,WACE,KAAKnC,MAAL,CAAYsB,OAAZ,KAAwB,IAAxB,IACA,CAAC,KAAK3B,QAAL,CAAcD,SAAd,CAAwB0C,QAAxB,CAAiC,KAAjC,CADD,IAEA,CAAC7D,wBAAwB,EAH3B;AAKD;;AA9Se;AAiTlB,OAAO,MAAe8D,oBAAf,SAGG/C,WAHH,CAG8B;AACnC;AACF;AACA;AACA;AACEgD,EAAAA,QAAQ,CAACjC,QAAD,EAA+D;AACrE,SAAKV,QAAL,CAAc2C,QAAd,GAAyBjC,QAAzB;AACA,SAAKV,QAAL,CAAcD,SAAd,CAAwBlB,aAAa,CAACI,MAAtC,IAAgD,KAAKc,SAAL,CAAeW,QAAf,CAAhD;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEkC,EAAAA,QAAQ,CACNlC,QADM,EAIN;AACA,SAAKV,QAAL,CAAc4C,QAAd,GAAyBlC,QAAzB;AACA,SAAKV,QAAL,CAAcD,SAAd,CAAwBlB,aAAa,CAACK,MAAtC,IAAgD,KAAKa,SAAL,CAAeW,QAAf,CAAhD;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEmC,EAAAA,gBAAgB,CAACA,gBAAD,EAA4B;AAC1C,SAAKxC,MAAL,CAAYwC,gBAAZ,GAA+BA,gBAA/B;AACA,WAAO,IAAP;AACD;;AAlCkC", "sourcesContent": ["import {\n  HitSlop,\n  CommonGestureConfig,\n  GestureTouchEvent,\n  GestureStateChangeEvent,\n  GestureUpdateEvent,\n  ActiveCursor,\n  MouseButton,\n} from '../gestureHandlerCommon';\nimport { getNextHandlerTag } from '../getNextHandlerTag';\nimport { GestureStateManagerType } from './gestureStateManager';\nimport type {\n  FlingGestureHandlerEventPayload,\n  ForceTouchGestureHandlerEventPayload,\n  LongPressGestureHandlerEventPayload,\n  PanGestureHandlerEventPayload,\n  PinchGestureHandlerEventPayload,\n  RotationGestureHandlerEventPayload,\n  TapGestureHandlerEventPayload,\n  NativeViewGestureHandlerPayload,\n  HoverGestureHandlerEventPayload,\n} from '../GestureHandlerEventPayload';\nimport { isRemoteDebuggingEnabled } from '../../utils';\n\nexport type GestureType =\n  | BaseGesture<Record<string, unknown>>\n  | BaseGesture<Record<string, never>>\n  | BaseGesture<TapGestureHandlerEventPayload>\n  | BaseGesture<PanGestureHandlerEventPayload>\n  | BaseGesture<LongPressGestureHandlerEventPayload>\n  | BaseGesture<RotationGestureHandlerEventPayload>\n  | BaseGesture<PinchGestureHandlerEventPayload>\n  | BaseGesture<FlingGestureHandlerEventPayload>\n  | BaseGesture<ForceTouchGestureHandlerEventPayload>\n  | BaseGesture<NativeViewGestureHandlerPayload>\n  | BaseGesture<HoverGestureHandlerEventPayload>;\n\nexport type GestureRef =\n  | number\n  | GestureType\n  | React.RefObject<GestureType | undefined>\n  | React.RefObject<React.ComponentType | undefined>; // Allow adding a ref to a gesture handler\nexport interface BaseGestureConfig\n  extends CommonGestureConfig,\n    Record<string, unknown> {\n  ref?: React.MutableRefObject<GestureType | undefined>;\n  requireToFail?: GestureRef[];\n  simultaneousWith?: GestureRef[];\n  blocksHandlers?: GestureRef[];\n  needsPointerData?: boolean;\n  manualActivation?: boolean;\n  runOnJS?: boolean;\n  testId?: string;\n  cancelsTouchesInView?: boolean;\n}\n\ntype TouchEventHandlerType = (\n  event: GestureTouchEvent,\n  stateManager: GestureStateManagerType\n) => void;\n\nexport type HandlerCallbacks<EventPayloadT extends Record<string, unknown>> = {\n  gestureId: number;\n  handlerTag: number;\n  onBegin?: (event: GestureStateChangeEvent<EventPayloadT>) => void;\n  onStart?: (event: GestureStateChangeEvent<EventPayloadT>) => void;\n  onEnd?: (\n    event: GestureStateChangeEvent<EventPayloadT>,\n    success: boolean\n  ) => void;\n  onFinalize?: (\n    event: GestureStateChangeEvent<EventPayloadT>,\n    success: boolean\n  ) => void;\n  onUpdate?: (event: GestureUpdateEvent<EventPayloadT>) => void;\n  onChange?: (event: any) => void;\n  onTouchesDown?: TouchEventHandlerType;\n  onTouchesMove?: TouchEventHandlerType;\n  onTouchesUp?: TouchEventHandlerType;\n  onTouchesCancelled?: TouchEventHandlerType;\n  changeEventCalculator?: (\n    current: GestureUpdateEvent<Record<string, unknown>>,\n    previous?: GestureUpdateEvent<Record<string, unknown>>\n  ) => GestureUpdateEvent<Record<string, unknown>>;\n  isWorklet: boolean[];\n};\n\nexport const CALLBACK_TYPE = {\n  UNDEFINED: 0,\n  BEGAN: 1,\n  START: 2,\n  UPDATE: 3,\n  CHANGE: 4,\n  END: 5,\n  FINALIZE: 6,\n  TOUCHES_DOWN: 7,\n  TOUCHES_MOVE: 8,\n  TOUCHES_UP: 9,\n  TOUCHES_CANCELLED: 10,\n} as const;\n\n// Allow using CALLBACK_TYPE as object and type\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type CALLBACK_TYPE = (typeof CALLBACK_TYPE)[keyof typeof CALLBACK_TYPE];\n\nexport abstract class Gesture {\n  /**\n   * Return array of gestures, providing the same interface for creating and updating\n   * handlers, no matter which object was used to create gesture instance.\n   */\n  abstract toGestureArray(): GestureType[];\n\n  /**\n   * Assign handlerTag to the gesture instance and set ref.current (if a ref is set)\n   */\n  abstract initialize(): void;\n\n  /**\n   * Make sure that values of properties defining relations are arrays. Do any necessary\n   * preprocessing required to configure relations between handlers. Called just before\n   * updating the handler on the native side.\n   */\n  abstract prepare(): void;\n}\n\nlet nextGestureId = 0;\nexport abstract class BaseGesture<\n  EventPayloadT extends Record<string, unknown>,\n> extends Gesture {\n  private gestureId = -1;\n  public handlerTag = -1;\n  public handlerName = '';\n  public config: BaseGestureConfig = {};\n  public handlers: HandlerCallbacks<EventPayloadT> = {\n    gestureId: -1,\n    handlerTag: -1,\n    isWorklet: [],\n  };\n\n  constructor() {\n    super();\n\n    // Used to check whether the gesture config has been updated when wrapping it\n    // with `useMemo`. Since every config will have a unique id, when the dependencies\n    // don't change, the config won't be recreated and the id will stay the same.\n    // If the id is different, it means that the config has changed and the gesture\n    // needs to be updated.\n    this.gestureId = nextGestureId++;\n    this.handlers.gestureId = this.gestureId;\n  }\n\n  private addDependency(\n    key: 'simultaneousWith' | 'requireToFail' | 'blocksHandlers',\n    gesture: Exclude<GestureRef, number>\n  ) {\n    const value = this.config[key];\n    this.config[key] = value\n      ? Array<GestureRef>().concat(value, gesture)\n      : [gesture];\n  }\n\n  /**\n   * Sets a `ref` to the gesture object, allowing for interoperability with the old API.\n   * @param ref\n   */\n  withRef(ref: React.MutableRefObject<GestureType | undefined>) {\n    this.config.ref = ref;\n    return this;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  protected isWorklet(callback: Function) {\n    // @ts-ignore if callback is a worklet, the property will be available, if not then the check will return false\n    return callback.__workletHash !== undefined;\n  }\n\n  /**\n   * Set the callback that is being called when given gesture handler starts receiving touches.\n   * At the moment of this callback the handler is in `BEGAN` state and we don't know yet if it will recognize the gesture at all.\n   * @param callback\n   */\n  onBegin(callback: (event: GestureStateChangeEvent<EventPayloadT>) => void) {\n    this.handlers.onBegin = callback;\n    this.handlers.isWorklet[CALLBACK_TYPE.BEGAN] = this.isWorklet(callback);\n    return this;\n  }\n\n  /**\n   * Set the callback that is being called when the gesture is recognized by the handler and it transitions to the `ACTIVE` state.\n   * @param callback\n   */\n  onStart(callback: (event: GestureStateChangeEvent<EventPayloadT>) => void) {\n    this.handlers.onStart = callback;\n    this.handlers.isWorklet[CALLBACK_TYPE.START] = this.isWorklet(callback);\n    return this;\n  }\n\n  /**\n   * Set the callback that is being called when the gesture that was recognized by the handler finishes and handler reaches `END` state.\n   * It will be called only if the handler was previously in the `ACTIVE` state.\n   * @param callback\n   */\n  onEnd(\n    callback: (\n      event: GestureStateChangeEvent<EventPayloadT>,\n      success: boolean\n    ) => void\n  ) {\n    this.handlers.onEnd = callback;\n    // @ts-ignore if callback is a worklet, the property will be available, if not then the check will return false\n    this.handlers.isWorklet[CALLBACK_TYPE.END] = this.isWorklet(callback);\n    return this;\n  }\n\n  /**\n   * Set the callback that is being called when the handler finalizes handling gesture - the gesture was recognized and has finished or it failed to recognize.\n   * @param callback\n   */\n  onFinalize(\n    callback: (\n      event: GestureStateChangeEvent<EventPayloadT>,\n      success: boolean\n    ) => void\n  ) {\n    this.handlers.onFinalize = callback;\n    // @ts-ignore if callback is a worklet, the property will be available, if not then the check will return false\n    this.handlers.isWorklet[CALLBACK_TYPE.FINALIZE] = this.isWorklet(callback);\n    return this;\n  }\n\n  /**\n   * Set the `onTouchesDown` callback which is called every time a pointer is placed on the screen.\n   * @param callback\n   */\n  onTouchesDown(callback: TouchEventHandlerType) {\n    this.config.needsPointerData = true;\n    this.handlers.onTouchesDown = callback;\n    this.handlers.isWorklet[CALLBACK_TYPE.TOUCHES_DOWN] =\n      this.isWorklet(callback);\n\n    return this;\n  }\n\n  /**\n   * Set the `onTouchesMove` callback which is called every time a pointer is moved on the screen.\n   * @param callback\n   */\n  onTouchesMove(callback: TouchEventHandlerType) {\n    this.config.needsPointerData = true;\n    this.handlers.onTouchesMove = callback;\n    this.handlers.isWorklet[CALLBACK_TYPE.TOUCHES_MOVE] =\n      this.isWorklet(callback);\n\n    return this;\n  }\n\n  /**\n   * Set the `onTouchesUp` callback which is called every time a pointer is lifted from the screen.\n   * @param callback\n   */\n  onTouchesUp(callback: TouchEventHandlerType) {\n    this.config.needsPointerData = true;\n    this.handlers.onTouchesUp = callback;\n    this.handlers.isWorklet[CALLBACK_TYPE.TOUCHES_UP] =\n      this.isWorklet(callback);\n\n    return this;\n  }\n\n  /**\n   * Set the `onTouchesCancelled` callback which is called every time a pointer stops being tracked, for example when the gesture finishes.\n   * @param callback\n   */\n  onTouchesCancelled(callback: TouchEventHandlerType) {\n    this.config.needsPointerData = true;\n    this.handlers.onTouchesCancelled = callback;\n    this.handlers.isWorklet[CALLBACK_TYPE.TOUCHES_CANCELLED] =\n      this.isWorklet(callback);\n\n    return this;\n  }\n\n  /**\n   * Indicates whether the given handler should be analyzing stream of touch events or not.\n   * @param enabled\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#enabledvalue-boolean\n   */\n  enabled(enabled: boolean) {\n    this.config.enabled = enabled;\n    return this;\n  }\n\n  /**\n   * When true the handler will cancel or fail recognition (depending on its current state) whenever the finger leaves the area of the connected view.\n   * @param value\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#shouldcancelwhenoutsidevalue-boolean\n   */\n  shouldCancelWhenOutside(value: boolean) {\n    this.config.shouldCancelWhenOutside = value;\n    return this;\n  }\n\n  /**\n   * This parameter enables control over what part of the connected view area can be used to begin recognizing the gesture.\n   * When a negative number is provided the bounds of the view will reduce the area by the given number of points in each of the sides evenly.\n   * @param hitSlop\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#hitslopsettings\n   */\n  hitSlop(hitSlop: HitSlop) {\n    this.config.hitSlop = hitSlop;\n    return this;\n  }\n\n  /**\n   * #### Web only\n   * This parameter allows to specify which `cursor` should be used when gesture activates.\n   * Supports all CSS cursor values (e.g. `\"grab\"`, `\"zoom-in\"`). Default value is set to `\"auto\"`.\n   * @param activeCursor\n   */\n  activeCursor(activeCursor: ActiveCursor) {\n    this.config.activeCursor = activeCursor;\n    return this;\n  }\n\n  /**\n   * #### Web & Android only\n   * Allows users to choose which mouse button should handler respond to.\n   * Arguments can be combined using `|` operator, e.g. `mouseButton(MouseButton.LEFT | MouseButton.RIGHT)`.\n   * Default value is set to `MouseButton.LEFT`.\n   * @param mouseButton\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#mousebuttonvalue-mousebutton-web--android-only\n   */\n  mouseButton(mouseButton: MouseButton) {\n    this.config.mouseButton = mouseButton;\n    return this;\n  }\n\n  /**\n   * When `react-native-reanimated` is installed, the callbacks passed to the gestures are automatically workletized and run on the UI thread when called.\n   * This option allows for changing this behavior: when `true`, all the callbacks will be run on the JS thread instead of the UI thread, regardless of whether they are worklets or not.\n   * Defaults to `false`.\n   * @param runOnJS\n   */\n  runOnJS(runOnJS: boolean) {\n    this.config.runOnJS = runOnJS;\n    return this;\n  }\n\n  /**\n   * Allows gestures across different components to be recognized simultaneously.\n   * @param gestures\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#simultaneouswithexternalgesture\n   */\n  simultaneousWithExternalGesture(...gestures: Exclude<GestureRef, number>[]) {\n    for (const gesture of gestures) {\n      this.addDependency('simultaneousWith', gesture);\n    }\n    return this;\n  }\n\n  /**\n   * Allows to delay activation of the handler until all handlers passed as arguments to this method fail (or don't begin at all).\n   * @param gestures\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#requireexternalgesturetofail\n   */\n  requireExternalGestureToFail(...gestures: Exclude<GestureRef, number>[]) {\n    for (const gesture of gestures) {\n      this.addDependency('requireToFail', gesture);\n    }\n    return this;\n  }\n\n  /**\n   * Works similarily to `requireExternalGestureToFail` but the direction of the relation is reversed - instead of being one-to-many relation, it's many-to-one.\n   * @param gestures\n   * @see https://docs.swmansion.com/react-native-gesture-handler/docs/fundamentals/gesture-composition/#blocksexternalgesture\n   */\n  blocksExternalGesture(...gestures: Exclude<GestureRef, number>[]) {\n    for (const gesture of gestures) {\n      this.addDependency('blocksHandlers', gesture);\n    }\n    return this;\n  }\n\n  /**\n   * Sets a `testID` property for gesture object, allowing for querying for it in tests.\n   * @param id\n   */\n  withTestId(id: string) {\n    this.config.testId = id;\n    return this;\n  }\n\n  /**\n   * #### iOS only\n   * When `true`, the handler will cancel touches for native UI components (`UIButton`, `UISwitch`, etc) it's attached to when it becomes `ACTIVE`.\n   * Default value is `true`.\n   * @param value\n   */\n  cancelsTouchesInView(value: boolean) {\n    this.config.cancelsTouchesInView = value;\n    return this;\n  }\n\n  initialize() {\n    this.handlerTag = getNextHandlerTag();\n\n    this.handlers = { ...this.handlers, handlerTag: this.handlerTag };\n\n    if (this.config.ref) {\n      this.config.ref.current = this as GestureType;\n    }\n  }\n\n  toGestureArray(): GestureType[] {\n    return [this as GestureType];\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  prepare() {}\n\n  get shouldUseReanimated(): boolean {\n    // Use Reanimated when runOnJS isn't set explicitly,\n    // all defined callbacks are worklets\n    // and remote debugging is disabled\n    return (\n      this.config.runOnJS !== true &&\n      !this.handlers.isWorklet.includes(false) &&\n      !isRemoteDebuggingEnabled()\n    );\n  }\n}\n\nexport abstract class ContinousBaseGesture<\n  EventPayloadT extends Record<string, unknown>,\n  EventChangePayloadT extends Record<string, unknown>,\n> extends BaseGesture<EventPayloadT> {\n  /**\n   * Set the callback that is being called every time the gesture receives an update while it's active.\n   * @param callback\n   */\n  onUpdate(callback: (event: GestureUpdateEvent<EventPayloadT>) => void) {\n    this.handlers.onUpdate = callback;\n    this.handlers.isWorklet[CALLBACK_TYPE.UPDATE] = this.isWorklet(callback);\n    return this;\n  }\n\n  /**\n   * Set the callback that is being called every time the gesture receives an update while it's active.\n   * This callback will receive information about change in value in relation to the last received event.\n   * @param callback\n   */\n  onChange(\n    callback: (\n      event: GestureUpdateEvent<EventPayloadT & EventChangePayloadT>\n    ) => void\n  ) {\n    this.handlers.onChange = callback;\n    this.handlers.isWorklet[CALLBACK_TYPE.CHANGE] = this.isWorklet(callback);\n    return this;\n  }\n\n  /**\n   * When `true` the handler will not activate by itself even if its activation criteria are met.\n   * Instead you can manipulate its state using state manager.\n   * @param manualActivation\n   */\n  manualActivation(manualActivation: boolean) {\n    this.config.manualActivation = manualActivation;\n    return this;\n  }\n}\n"]}