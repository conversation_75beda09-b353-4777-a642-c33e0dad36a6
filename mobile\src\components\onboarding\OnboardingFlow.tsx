import React, { useState } from 'react';
import { SplashScreen } from './SplashScreen';
import { OnboardingScreen } from './OnboardingScreen';

interface OnboardingFlowProps {
  onComplete: () => void;
}

export function OnboardingFlow({ onComplete }: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState<'splash' | 'onboarding'>('splash');

  const handleSplashComplete = () => {
    setCurrentStep('onboarding');
  };

  const handleOnboardingComplete = () => {
    onComplete();
  };

  const handleSkip = () => {
    onComplete();
  };

  if (currentStep === 'splash') {
    return <SplashScreen onComplete={handleSplashComplete} />;
  }

  return (
    <OnboardingScreen
      onComplete={handleOnboardingComplete}
      onSkip={handleSkip}
    />
  );
}
