import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  PanResponder,
  Animated,
} from 'react-native';
import { OnboardingSlide } from './OnboardingSlide';
import { OnboardingProps, OnboardingSlideData } from './types';
import { useTranslation } from '../../i18n';

const { width } = Dimensions.get('window');

export function OnboardingScreen({ onComplete, onSkip }: OnboardingProps) {
  const { t } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);
  const slideAnim = useRef(new Animated.Value(0)).current;

  const onboardingData: OnboardingSlideData[] = [
    {
      id: 1,
      title: t('onboarding.slide1.title'),
      subtitle: t('onboarding.slide1.subtitle'),
      description: t('onboarding.slide1.description'),
      image: 'slide1',
      buttonText: t('onboarding.slide1.button'),
    },
    {
      id: 2,
      title: t('onboarding.slide2.title'),
      subtitle: t('onboarding.slide2.subtitle'),
      description: t('onboarding.slide2.description'),
      image: 'slide2',
      buttonText: t('onboarding.slide2.button'),
    },
    {
      id: 3,
      title: t('onboarding.slide3.title'),
      subtitle: t('onboarding.slide3.subtitle'),
      description: t('onboarding.slide3.description'),
      image: 'slide3',
      buttonText: t('onboarding.slide3.button'),
      isLast: true,
    },
  ];

  const nextSlide = () => {
    if (currentSlide < onboardingData.length - 1) {
      const nextIndex = currentSlide + 1;
      
      Animated.timing(slideAnim, {
        toValue: -nextIndex * width,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setCurrentSlide(nextIndex);
      });
    } else {
      onComplete();
    }
  };

  const previousSlide = () => {
    if (currentSlide > 0) {
      const prevIndex = currentSlide - 1;
      
      Animated.timing(slideAnim, {
        toValue: -prevIndex * width,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setCurrentSlide(prevIndex);
      });
    }
  };

  const skipOnboarding = () => {
    if (onSkip) {
      onSkip();
    } else {
      onComplete();
    }
  };

  // Pan responder for swipe gestures
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return Math.abs(gestureState.dx) > Math.abs(gestureState.dy) && Math.abs(gestureState.dx) > 20;
      },
      onPanResponderMove: (_, gestureState) => {
        const newValue = -currentSlide * width + gestureState.dx;
        slideAnim.setValue(newValue);
      },
      onPanResponderRelease: (_, gestureState) => {
        const threshold = width * 0.3;
        
        if (gestureState.dx > threshold && currentSlide > 0) {
          // Swipe right - go to previous slide
          previousSlide();
        } else if (gestureState.dx < -threshold && currentSlide < onboardingData.length - 1) {
          // Swipe left - go to next slide
          nextSlide();
        } else {
          // Snap back to current slide
          Animated.timing(slideAnim, {
            toValue: -currentSlide * width,
            duration: 200,
            useNativeDriver: true,
          }).start();
        }
      },
    })
  ).current;

  return (
    <View style={styles.container} {...panResponder.panHandlers}>
      <Animated.View
        style={[
          styles.slidesContainer,
          {
            transform: [{ translateX: slideAnim }],
          },
        ]}
      >
        {onboardingData.map((slide, index) => (
          <View key={slide.id} style={styles.slideWrapper}>
            <OnboardingSlide
              slide={slide}
              isActive={index === currentSlide}
              onNext={nextSlide}
              onSkip={onSkip}
              currentIndex={index}
              totalSlides={onboardingData.length}
            />
          </View>
        ))}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  slidesContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  slideWrapper: {
    width,
    flex: 1,
  },
});
