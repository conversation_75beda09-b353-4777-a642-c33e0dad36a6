import React, { useState } from "react";
import { Dimensions, StyleSheet, View } from "react-native";
import { useTranslation } from "../../i18n";
import { OnboardingSlide } from "./OnboardingSlideSimple";
import { OnboardingProps, OnboardingSlideData } from "./types";

const { width } = Dimensions.get("window");

export function OnboardingScreen({ onComplete, onSkip }: OnboardingProps) {
  const { t } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);

  const onboardingData: OnboardingSlideData[] = [
    {
      id: 1,
      title: t("onboarding.slide1.title"),
      subtitle: t("onboarding.slide1.subtitle"),
      description: t("onboarding.slide1.description"),
      image: "slide1",
      buttonText: t("onboarding.slide1.button"),
    },
    {
      id: 2,
      title: t("onboarding.slide2.title"),
      subtitle: t("onboarding.slide2.subtitle"),
      description: t("onboarding.slide2.description"),
      image: "slide2",
      buttonText: t("onboarding.slide2.button"),
    },
    {
      id: 3,
      title: t("onboarding.slide3.title"),
      subtitle: t("onboarding.slide3.subtitle"),
      description: t("onboarding.slide3.description"),
      image: "slide3",
      buttonText: t("onboarding.slide3.button"),
      isLast: true,
    },
  ];

  const nextSlide = () => {
    if (currentSlide < onboardingData.length - 1) {
      setCurrentSlide(currentSlide + 1);
    } else {
      onComplete();
    }
  };

  return (
    <View style={styles.container}>
      <OnboardingSlide
        slide={onboardingData[currentSlide]}
        isActive={true}
        onNext={nextSlide}
        onSkip={onSkip}
        currentIndex={currentSlide}
        totalSlides={onboardingData.length}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  slidesContainer: {
    flexDirection: "row",
    flex: 1,
  },
  slideWrapper: {
    width,
    flex: 1,
  },
});
