{"version": 3, "names": ["React", "extractProps", "propsAndStyles", "extractFont", "extractTransform", "<PERSON><PERSON><PERSON>", "RNSVGGroup", "G", "displayName", "setNativeProps", "props", "_this$root", "matrix", "root", "render", "prop", "extractedProps", "font", "hasProps", "createElement", "_extends", "ref", "refMethod", "children", "obj", "_"], "sourceRoot": "../../../src", "sources": ["elements/G.tsx"], "mappings": ";AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,IAAIC,cAAc,QAAQ,6BAA6B;AAC1E,SAASC,WAAW,QAAQ,4BAA4B;AACxD,OAAOC,gBAAgB,MAAM,iCAAiC;AAM9D,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,UAAU,MAAM,gCAAgC;AAQvD,eAAe,MAAMC,CAAC,SAAYF,KAAK,CAAa;EAClD,OAAOG,WAAW,GAAG,GAAG;EAExBC,cAAc,GACZC,KAGG,IACA;IAAA,IAAAC,UAAA;IACH,MAAMC,MAAM,GAAG,CAACF,KAAK,CAACE,MAAM,IAAIR,gBAAgB,CAACM,KAAK,CAAC;IACvD,IAAIE,MAAM,EAAE;MACVF,KAAK,CAACE,MAAM,GAAGA,MAAM;IACvB;IACA,CAAAD,UAAA,OAAI,CAACE,IAAI,cAAAF,UAAA,eAATA,UAAA,CAAWF,cAAc,CAACC,KAAK,CAAC;EAClC,CAAC;EAEDI,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEJ;IAAM,CAAC,GAAG,IAAI;IACtB,MAAMK,IAAI,GAAGb,cAAc,CAACQ,KAAK,CAAC;IAClC,MAAMM,cAAc,GAAGf,YAAY,CAACc,IAAI,EAAE,IAAI,CAAC;IAC/C,MAAME,IAAI,GAAGd,WAAW,CAACY,IAAI,CAAC;IAC9B,IAAIG,QAAQ,CAACD,IAAI,CAAC,EAAE;MAClBD,cAAc,CAACC,IAAI,GAAGA,IAAI;IAC5B;IACA,oBACEjB,KAAA,CAAAmB,aAAA,CAACb,UAAU,EAAAc,QAAA;MACTC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAoC;IAAE,GAC/DL,cAAc,GACjBN,KAAK,CAACa,QACG,CAAC;EAEjB;AACF;AAEA,MAAML,QAAQ,GAAIM,GAAW,IAAK;EAChC;EACA,KAAK,MAAMC,CAAC,IAAID,GAAG,EAAE;IACnB,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}