{"version": 3, "names": ["_codegenNativeComponent", "_interopRequireDefault", "require", "e", "__esModule", "default", "_default", "exports", "codegenNativeComponent", "interfaceOnly"], "sourceRoot": "../../../src", "sources": ["fabric/LinearGradientNativeComponent.ts"], "mappings": ";;;;;;AAAA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA6F,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,IAAAG,QAAA,GAAAC,OAAA,CAAAF,OAAA,GAoC9E,IAAAG,+BAAsB,EAAc,qBAAqB,EAAE;EACxEC,aAAa,EAAE;AACjB,CAAC,CAAC", "ignoreList": []}