{"version": 3, "sources": ["TouchableNativeFeedback.tsx"], "names": ["TouchableNativeFeedback", "RNTouchableNativeFeedback"], "mappings": ";;;;;;;AAAA;;AAEA;AACA;AACA;AACA,MAAMA,uBAAuB,GAAGC,oCAAhC;eAEeD,uB", "sourcesContent": ["import { TouchableNativeFeedback as RNTouchableNativeFeedback } from 'react-native';\n\n/**\n * @deprecated TouchableNativeFeedback will be removed in the future version of Gesture Handler. Use Pressable instead.\n */\nconst TouchableNativeFeedback = RNTouchableNativeFeedback;\n\nexport default TouchableNativeFeedback;\n"]}