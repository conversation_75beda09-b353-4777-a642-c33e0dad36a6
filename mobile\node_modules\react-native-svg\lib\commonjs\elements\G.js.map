{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_extractProps", "_extractText", "_extractTransform", "_interopRequireDefault", "_Shape", "_GroupNativeComponent", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "G", "<PERSON><PERSON><PERSON>", "displayName", "setNativeProps", "props", "_this$root", "matrix", "extractTransform", "root", "render", "prop", "propsAndStyles", "extractedProps", "extractProps", "font", "extractFont", "hasProps", "createElement", "ref", "refMethod", "children", "exports", "obj", "_"], "sourceRoot": "../../../src", "sources": ["elements/G.tsx"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AACA,IAAAG,iBAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAMA,IAAAK,MAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,qBAAA,GAAAF,sBAAA,CAAAJ,OAAA;AAAwD,SAAAI,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAAA,SAAAW,SAAA,WAAAA,QAAA,GAAAR,MAAA,CAAAS,MAAA,GAAAT,MAAA,CAAAS,MAAA,CAAAC,IAAA,eAAAb,CAAA,aAAAT,CAAA,MAAAA,CAAA,GAAAuB,SAAA,CAAAC,MAAA,EAAAxB,CAAA,UAAAM,CAAA,GAAAiB,SAAA,CAAAvB,CAAA,YAAAK,CAAA,IAAAC,CAAA,OAAAU,cAAA,CAAAC,IAAA,CAAAX,CAAA,EAAAD,CAAA,MAAAI,CAAA,CAAAJ,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAI,CAAA,KAAAW,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAQzC,MAAMG,CAAC,SAAYC,cAAK,CAAa;EAClD,OAAOC,WAAW,GAAG,GAAG;EAExBC,cAAc,GACZC,KAGG,IACA;IAAA,IAAAC,UAAA;IACH,MAAMC,MAAM,GAAG,CAACF,KAAK,CAACE,MAAM,IAAI,IAAAC,yBAAgB,EAACH,KAAK,CAAC;IACvD,IAAIE,MAAM,EAAE;MACVF,KAAK,CAACE,MAAM,GAAGA,MAAM;IACvB;IACA,CAAAD,UAAA,OAAI,CAACG,IAAI,cAAAH,UAAA,eAATA,UAAA,CAAWF,cAAc,CAACC,KAAK,CAAC;EAClC,CAAC;EAEDK,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEL;IAAM,CAAC,GAAG,IAAI;IACtB,MAAMM,IAAI,GAAG,IAAAC,4BAAc,EAACP,KAAK,CAAC;IAClC,MAAMQ,cAAc,GAAG,IAAAC,qBAAY,EAACH,IAAI,EAAE,IAAI,CAAC;IAC/C,MAAMI,IAAI,GAAG,IAAAC,wBAAW,EAACL,IAAI,CAAC;IAC9B,IAAIM,QAAQ,CAACF,IAAI,CAAC,EAAE;MAClBF,cAAc,CAACE,IAAI,GAAGA,IAAI;IAC5B;IACA,oBACEjD,KAAA,CAAAoD,aAAA,CAAC5C,qBAAA,CAAAG,OAAU,EAAAkB,QAAA;MACTwB,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAoC;IAAE,GAC/DN,cAAc,GACjBR,KAAK,CAACgB,QACG,CAAC;EAEjB;AACF;AAACC,OAAA,CAAA7C,OAAA,GAAAwB,CAAA;AAED,MAAMgB,QAAQ,GAAIM,GAAW,IAAK;EAChC;EACA,KAAK,MAAMC,CAAC,IAAID,GAAG,EAAE;IACnB,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}