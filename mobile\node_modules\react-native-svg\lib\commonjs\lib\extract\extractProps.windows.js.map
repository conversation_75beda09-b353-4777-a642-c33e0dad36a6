{"version": 3, "names": ["_extractFill", "_interopRequireDefault", "require", "_extractStroke", "_extractTransform", "_extractResponder", "_extractOpacity", "_util", "e", "__esModule", "default", "clipRules", "evenodd", "nonzero", "propsAndStyles", "props", "style", "Array", "isArray", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON>", "marker", "undefined", "matched", "match", "idPattern", "extractProps", "ref", "id", "opacity", "onLayout", "clipPath", "clipRule", "display", "mask", "markerStart", "markerMid", "markerEnd", "extracted", "inherited", "extractResponder", "extractFill", "extractStroke", "color", "length", "propList", "matrix", "extractTransform", "extractOpacity", "name", "String", "console", "warn", "extract", "instance", "withoutXY", "x", "y"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractProps.windows.ts"], "mappings": ";;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,iBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,iBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,eAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AAAoC,SAAAD,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAYpC,MAAMG,SAA+C,GAAG;EACtDC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AAEM,SAASC,cAAcA,CAACC,KAAwC,EAAE;EACvE,MAAM;IAAEC;EAAM,CAAC,GAAGD,KAAK;EACvB,OAAO,CAACC,KAAK,GACTD,KAAK,GACL;IACE,IAAIE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAGJ,KAAK,CAAC,GAAGA,KAAK,CAAC;IAC/D,GAAGD;EACL,CAAC;AACP;AAEA,SAASM,SAASA,CAACC,MAAe,EAAE;EAClC,IAAI,CAACA,MAAM,EAAE;IACX,OAAOC,SAAS;EAClB;EACA,MAAMC,OAAO,GAAGF,MAAM,CAACG,KAAK,CAACC,eAAS,CAAC;EACvC,OAAOF,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAGD,SAAS;AACzC;AAEe,SAASI,YAAYA,CAClCZ,KAgBW,EACXa,GAAW,EACX;EACA,MAAM;IACJC,EAAE;IACFC,OAAO;IACPC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,OAAO;IACPC,IAAI;IACJb,MAAM;IACNc,WAAW,GAAGd,MAAM;IACpBe,SAAS,GAAGf,MAAM;IAClBgB,SAAS,GAAGhB;EACd,CAAC,GAAGP,KAAK;EACT,MAAMwB,SAAyB,GAAG,CAAC,CAAC;EAEpC,MAAMC,SAAmB,GAAG,EAAE;EAC9B,IAAAC,yBAAgB,EAACF,SAAS,EAAExB,KAAK,EAAEa,GAAG,CAAC;EACvC,IAAAc,oBAAW,EAACH,SAAS,EAAExB,KAAK,EAAEyB,SAAS,CAAC;EACxC,IAAAG,sBAAa,EAACJ,SAAS,EAAExB,KAAK,EAAEyB,SAAS,CAAC;EAC1C,IAAIzB,KAAK,CAAC6B,KAAK,EAAE;IACfL,SAAS,CAACK,KAAK,GAAG7B,KAAK,CAAC6B,KAAK;EAC/B;EAEA,IAAIJ,SAAS,CAACK,MAAM,EAAE;IACpBN,SAAS,CAACO,QAAQ,GAAGN,SAAS;EAChC;EAEA,MAAMO,MAAM,GAAG,IAAAC,yBAAgB,EAACjC,KAAK,CAAC;EACtC,IAAIgC,MAAM,KAAK,IAAI,EAAE;IACnBR,SAAS,CAACQ,MAAM,GAAGA,MAAM;EAC3B;EAEA,IAAIjB,OAAO,IAAI,IAAI,EAAE;IACnBS,SAAS,CAACT,OAAO,GAAG,IAAAmB,uBAAc,EAACnB,OAAO,CAAC;EAC7C;EAEA,IAAII,OAAO,IAAI,IAAI,EAAE;IACnBK,SAAS,CAACL,OAAO,GAAGA,OAAO,KAAK,MAAM,GAAG,MAAM,GAAGX,SAAS;EAC7D;EAEA,IAAIQ,QAAQ,EAAE;IACZQ,SAAS,CAACR,QAAQ,GAAGA,QAAQ;EAC/B;EAEA,IAAIK,WAAW,EAAE;IACfG,SAAS,CAACH,WAAW,GAAGf,SAAS,CAACe,WAAW,CAAC;EAChD;EACA,IAAIC,SAAS,EAAE;IACbE,SAAS,CAACF,SAAS,GAAGhB,SAAS,CAACgB,SAAS,CAAC;EAC5C;EACA,IAAIC,SAAS,EAAE;IACbC,SAAS,CAACD,SAAS,GAAGjB,SAAS,CAACiB,SAAS,CAAC;EAC5C;EAEA,IAAIT,EAAE,EAAE;IACNU,SAAS,CAACW,IAAI,GAAGC,MAAM,CAACtB,EAAE,CAAC;EAC7B;EAEA,IAAII,QAAQ,EAAE;IACZM,SAAS,CAACN,QAAQ,GAAGtB,SAAS,CAACsB,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;EACxD;EACA,IAAID,QAAQ,EAAE;IACZ,MAAMR,OAAO,GAAGQ,QAAQ,CAACP,KAAK,CAACC,eAAS,CAAC;IACzC,IAAIF,OAAO,EAAE;MACXe,SAAS,CAACP,QAAQ,GAAGR,OAAO,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM;MACL4B,OAAO,CAACC,IAAI,CACV,qEAAqE,GACnErB,QAAQ,GACR,GACJ,CAAC;IACH;EACF;EAEA,IAAIG,IAAI,EAAE;IACR,MAAMX,OAAO,GAAGW,IAAI,CAACV,KAAK,CAACC,eAAS,CAAC;IAErC,IAAIF,OAAO,EAAE;MACXe,SAAS,CAACJ,IAAI,GAAGX,OAAO,CAAC,CAAC,CAAC;IAC7B,CAAC,MAAM;MACL4B,OAAO,CAACC,IAAI,CACV,6DAA6D,GAC3DlB,IAAI,GACJ,GACJ,CAAC;IACH;EACF;EAEA,OAAOI,SAAS;AAClB;AAEO,SAASe,OAAOA,CACrBC,QAAgB,EAChBxC,KAAwC,EACxC;EACA,OAAOY,YAAY,CAACb,cAAc,CAACC,KAAK,CAAC,EAAEwC,QAAQ,CAAC;AACtD;AAEO,SAASC,SAASA,CACvBD,QAAgB,EAChBxC,KAAwC,EACxC;EACA,OAAOY,YAAY,CAAC;IAAE,GAAGb,cAAc,CAACC,KAAK,CAAC;IAAE0C,CAAC,EAAE,IAAI;IAAEC,CAAC,EAAE;EAAK,CAAC,EAAEH,QAAQ,CAAC;AAC/E", "ignoreList": []}