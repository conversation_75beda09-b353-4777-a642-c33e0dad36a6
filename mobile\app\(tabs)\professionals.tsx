import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  FlatList,
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { Card } from "../../src/components/ui";
import {
  BorderRadius,
  FontSizes,
  Spacing,
  TunaWorkColors,
} from "../../src/constants";
import { useTranslation } from "../../src/i18n";
import { featuredFreelancers, Freelancer } from "../../src/lib/data";

export default function ProfessionalsScreen() {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = [
    { key: "all", label: "Tous", icon: "grid" },
    { key: "development", label: "Développement", icon: "code" },
    { key: "design", label: "Design", icon: "color-palette" },
    { key: "marketing", label: "Marketing", icon: "megaphone" },
    { key: "writing", label: "Rédaction", icon: "document-text" },
  ];

  // Simulate more freelancers by duplicating the featured ones with different IDs
  const allFreelancers = [
    ...featuredFreelancers,
    ...featuredFreelancers.map((freelancer, index) => ({
      ...freelancer,
      id: `${freelancer.id}_${index + 10}`,
      firstName: freelancer.firstName + " " + (index + 1),
    })),
  ];

  const filteredFreelancers = allFreelancers.filter((freelancer) => {
    const matchesSearch =
      searchQuery === "" ||
      freelancer.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      freelancer.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      freelancer.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      freelancer.skills.some((skill) =>
        skill.toLowerCase().includes(searchQuery.toLowerCase())
      );

    const matchesCategory =
      selectedCategory === "all" ||
      (selectedCategory === "development" &&
        freelancer.skills.some((skill) =>
          ["React", "Node.js", "TypeScript", "JavaScript", "Python"].includes(
            skill
          )
        )) ||
      (selectedCategory === "design" &&
        freelancer.title.toLowerCase().includes("design")) ||
      (selectedCategory === "marketing" &&
        freelancer.title.toLowerCase().includes("marketing"));

    return matchesSearch && matchesCategory;
  });

  const renderFreelancer = ({ item }: { item: Freelancer }) => (
    <TouchableOpacity
      style={styles.freelancerCard}
      onPress={() => router.push(`/freelancer/${item.id}` as any)}
    >
      <Card padding="md">
        <View style={styles.freelancerHeader}>
          <View style={styles.avatarContainer}>
            <Image source={{ uri: item.avatar }} style={styles.avatar} />
            <View
              style={[
                styles.onlineIndicator,
                { backgroundColor: item.isOnline ? "#10B981" : "#6B7280" },
              ]}
            />
          </View>
          <View style={styles.freelancerInfo}>
            <Text style={styles.freelancerName}>
              {item.firstName} {item.lastName}
            </Text>
            <Text style={styles.freelancerTitle}>{item.title}</Text>
            <Text style={styles.location}>
              <Ionicons
                name="location"
                size={12}
                color={TunaWorkColors.secondary[500]}
              />{" "}
              {item.location}
            </Text>
          </View>
          <View style={styles.rateContainer}>
            <Text style={styles.hourlyRate}>${item.hourlyRate}/h</Text>
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={14} color="#F59E0B" />
              <Text style={styles.rating}>{item.rating}</Text>
              <Text style={styles.reviewCount}>({item.reviewCount})</Text>
            </View>
          </View>
        </View>

        <Text style={styles.description} numberOfLines={2}>
          {item.description}
        </Text>

        <View style={styles.skillsContainer}>
          {item.skills.slice(0, 3).map((skill, index) => (
            <View key={index} style={styles.skillTag}>
              <Text style={styles.skillText}>{skill}</Text>
            </View>
          ))}
          {item.skills.length > 3 && (
            <Text style={styles.moreSkills}>+{item.skills.length - 3}</Text>
          )}
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{item.completedProjects}</Text>
            <Text style={styles.statLabel}>Projets</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{item.successRate}%</Text>
            <Text style={styles.statLabel}>Succès</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{item.responseTime}</Text>
            <Text style={styles.statLabel}>Réponse</Text>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{t("professionals.title")}</Text>
        <TouchableOpacity style={styles.filterButton}>
          <Ionicons
            name="options"
            size={24}
            color={TunaWorkColors.secondary[600]}
          />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons
            name="search"
            size={20}
            color={TunaWorkColors.secondary[400]}
          />
          <TextInput
            style={styles.searchInput}
            placeholder={t("professionals.searchPlaceholder")}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={TunaWorkColors.secondary[400]}
          />
        </View>
      </View>

      {/* Categories */}
      <FlatList
        data={categories}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.categoryButton,
              selectedCategory === item.key && styles.activeCategoryButton,
            ]}
            onPress={() => setSelectedCategory(item.key)}
          >
            <Ionicons
              name={item.icon as any}
              size={16}
              color={
                selectedCategory === item.key
                  ? "#FFFFFF"
                  : TunaWorkColors.secondary[600]
              }
            />
            <Text
              style={[
                styles.categoryText,
                selectedCategory === item.key && styles.activeCategoryText,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        )}
        keyExtractor={(item) => item.key}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesContainer}
      />

      {/* Results Count */}
      <View style={styles.resultsContainer}>
        <Text style={styles.resultsText}>
          {filteredFreelancers.length} freelancer
          {filteredFreelancers.length > 1 ? "s" : ""} trouvé
          {filteredFreelancers.length > 1 ? "s" : ""}
        </Text>
      </View>

      {/* Freelancers List */}
      <FlatList
        data={filteredFreelancers}
        renderItem={renderFreelancer}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.freelancersList}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: TunaWorkColors.secondary[50],
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    backgroundColor: "#FFFFFF",
  },
  headerTitle: {
    fontSize: FontSizes["2xl"],
    fontWeight: "700",
    color: TunaWorkColors.secondary[900],
  },
  filterButton: {
    padding: Spacing.sm,
  },
  searchContainer: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    backgroundColor: "#FFFFFF",
  },
  searchInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: TunaWorkColors.secondary[100],
    borderRadius: BorderRadius.lg,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[900],
    marginLeft: Spacing.sm,
  },
  categoriesContainer: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    backgroundColor: "#FFFFFF",
  },
  categoryButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
    backgroundColor: TunaWorkColors.secondary[100],
    marginRight: Spacing.sm,
    gap: Spacing.xs,
  },
  activeCategoryButton: {
    backgroundColor: TunaWorkColors.primary[500],
  },
  categoryText: {
    fontSize: FontSizes.sm,
    fontWeight: "500",
    color: TunaWorkColors.secondary[600],
  },
  activeCategoryText: {
    color: "#FFFFFF",
  },
  resultsContainer: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  resultsText: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
  },
  freelancersList: {
    padding: Spacing.md,
  },
  freelancerCard: {
    marginBottom: Spacing.md,
  },
  freelancerHeader: {
    flexDirection: "row",
    marginBottom: Spacing.sm,
  },
  avatarContainer: {
    position: "relative",
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  onlineIndicator: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: "#FFFFFF",
  },
  freelancerInfo: {
    flex: 1,
    marginLeft: Spacing.sm,
  },
  freelancerName: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
  },
  freelancerTitle: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
    marginBottom: 2,
  },
  location: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.secondary[500],
  },
  rateContainer: {
    alignItems: "flex-end",
  },
  hourlyRate: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.primary[500],
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 2,
  },
  rating: {
    fontSize: FontSizes.sm,
    fontWeight: "500",
    color: TunaWorkColors.secondary[900],
    marginLeft: 2,
  },
  reviewCount: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.secondary[500],
    marginLeft: 2,
  },
  description: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[700],
    lineHeight: 20,
    marginBottom: Spacing.sm,
  },
  skillsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: Spacing.sm,
  },
  skillTag: {
    backgroundColor: TunaWorkColors.primary[100],
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.xs,
    marginBottom: Spacing.xs,
  },
  skillText: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.primary[700],
    fontWeight: "500",
  },
  moreSkills: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.secondary[500],
    alignSelf: "center",
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: TunaWorkColors.secondary[200],
  },
  statItem: {
    alignItems: "center",
  },
  statValue: {
    fontSize: FontSizes.base,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
  },
  statLabel: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.secondary[500],
  },
});
