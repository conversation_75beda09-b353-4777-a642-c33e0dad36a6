import AsyncStorage from '@react-native-async-storage/async-storage';
import { useState, useEffect } from 'react';

// Import translations
import fr from './fr.json';
import ln from './ln.json';

export type Locale = 'fr' | 'ln';

export const translations = {
  fr,
  ln,
};

export const defaultLocale: Locale = 'fr';

const LOCALE_STORAGE_KEY = '@tunawork:locale';

// Hook for managing locale
export function useLocale() {
  const [locale, setLocale] = useState<Locale>(defaultLocale);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadLocale();
  }, []);

  const loadLocale = async () => {
    try {
      const savedLocale = await AsyncStorage.getItem(LOCALE_STORAGE_KEY);
      if (savedLocale && (savedLocale === 'fr' || savedLocale === 'ln')) {
        setLocale(savedLocale as Locale);
      }
    } catch (error) {
      console.error('Error loading locale:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const changeLocale = async (newLocale: Locale) => {
    try {
      await AsyncStorage.setItem(LOCALE_STORAGE_KEY, newLocale);
      setLocale(newLocale);
    } catch (error) {
      console.error('Error saving locale:', error);
    }
  };

  return {
    locale,
    changeLocale,
    isLoading,
  };
}

// Translation function
export function useTranslation() {
  const { locale } = useLocale();

  const t = (key: string, params?: Record<string, string | number>) => {
    const keys = key.split('.');
    let value: any = translations[locale];

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // Fallback to French if key not found
        value = translations.fr;
        for (const fallbackKey of keys) {
          if (value && typeof value === 'object' && fallbackKey in value) {
            value = value[fallbackKey];
          } else {
            return key; // Return key if not found in fallback
          }
        }
        break;
      }
    }

    if (typeof value === 'string' && params) {
      return value.replace(/\{\{(\w+)\}\}/g, (match, paramKey) => {
        return params[paramKey]?.toString() || match;
      });
    }

    return typeof value === 'string' ? value : key;
  };

  return { t, locale };
}

// Available locales
export const availableLocales = [
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'ln', name: 'Lingala', flag: '🇨🇩' },
] as const;
