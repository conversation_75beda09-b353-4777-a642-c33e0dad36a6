{"version": 3, "sources": ["eventReceiver.ts"], "names": ["DeviceEventEmitter", "State", "TouchEventType", "<PERSON><PERSON><PERSON><PERSON>", "findOldGestureHandler", "GestureStateManager", "gestureHandlerEventSubscription", "gestureHandlerStateChangeEventSubscription", "gestureStateManagers", "Map", "lastUpdateEvent", "isStateChangeEvent", "event", "oldState", "isTouchEvent", "eventType", "onGestureHandlerEvent", "handler", "handlerTag", "UNDETERMINED", "state", "BEGAN", "handlers", "onBegin", "ACTIVE", "onStart", "END", "onEnd", "onFinalize", "undefined", "FAILED", "CANCELLED", "delete", "has", "set", "create", "manager", "get", "TOUCHES_DOWN", "onTouchesDown", "TOUCHES_MOVE", "onTouchesMove", "TOUCHES_UP", "onTouchesUp", "TOUCHES_CANCELLED", "onTouchesCancelled", "onUpdate", "onChange", "changeEventCalculator", "<PERSON><PERSON><PERSON><PERSON>", "nativeEvent", "onGestureStateChange", "onGestureEvent", "startListening", "stopListening", "addListener", "remove"], "mappings": "AAAA,SAASA,kBAAT,QAAwD,cAAxD;AACA,SAASC,KAAT,QAAsB,aAAtB;AACA,SAASC,cAAT,QAA+B,sBAA/B;AAMA,SAASC,WAAT,EAAsBC,qBAAtB,QAAmD,qBAAnD;AAEA,SACEC,mBADF,QAGO,uBAHP;AAKA,IAAIC,+BAA2D,GAAG,IAAlE;AACA,IAAIC,0CAAsE,GACxE,IADF;AAGA,MAAMC,oBAA0D,GAAG,IAAIC,GAAJ,EAAnE;AAKA,MAAMC,eAAmD,GAAG,EAA5D;;AAEA,SAASC,kBAAT,CACEC,KADF,EAEoC;AAClC;AACA,SAAOA,KAAK,CAACC,QAAN,IAAkB,IAAzB;AACD;;AAED,SAASC,YAAT,CACEF,KADF,EAE8B;AAC5B,SAAOA,KAAK,CAACG,SAAN,IAAmB,IAA1B;AACD;;AAED,OAAO,SAASC,qBAAT,CACLJ,KADK,EAEL;AAAA;;AACA,QAAMK,OAAO,GAAGd,WAAW,CAACS,KAAK,CAACM,UAAP,CAA3B;;AAIA,MAAID,OAAJ,EAAa;AACX,QAAIN,kBAAkB,CAACC,KAAD,CAAtB,EAA+B;AAC7B,UACEA,KAAK,CAACC,QAAN,KAAmBZ,KAAK,CAACkB,YAAzB,IACAP,KAAK,CAACQ,KAAN,KAAgBnB,KAAK,CAACoB,KAFxB,EAGE;AAAA;;AACA,sDAAAJ,OAAO,CAACK,QAAR,EAAiBC,OAAjB,wGAA2BX,KAA3B;AACD,OALD,MAKO,IACL,CAACA,KAAK,CAACC,QAAN,KAAmBZ,KAAK,CAACoB,KAAzB,IACCT,KAAK,CAACC,QAAN,KAAmBZ,KAAK,CAACkB,YAD3B,KAEAP,KAAK,CAACQ,KAAN,KAAgBnB,KAAK,CAACuB,MAHjB,EAIL;AAAA;;AACA,uDAAAP,OAAO,CAACK,QAAR,EAAiBG,OAAjB,yGAA2Bb,KAA3B;AACAF,QAAAA,eAAe,CAACO,OAAO,CAACK,QAAR,CAAiBJ,UAAlB,CAAf,GAA+CN,KAA/C;AACD,OAPM,MAOA,IAAIA,KAAK,CAACC,QAAN,KAAmBD,KAAK,CAACQ,KAAzB,IAAkCR,KAAK,CAACQ,KAAN,KAAgBnB,KAAK,CAACyB,GAA5D,EAAiE;AAAA;;AACtE,YAAId,KAAK,CAACC,QAAN,KAAmBZ,KAAK,CAACuB,MAA7B,EAAqC;AAAA;;AACnC,yDAAAP,OAAO,CAACK,QAAR,EAAiBK,KAAjB,yGAAyBf,KAAzB,EAAgC,IAAhC;AACD;;AACD,uDAAAK,OAAO,CAACK,QAAR,EAAiBM,UAAjB,yGAA8BhB,KAA9B,EAAqC,IAArC;AACAF,QAAAA,eAAe,CAACO,OAAO,CAACK,QAAR,CAAiBJ,UAAlB,CAAf,GAA+CW,SAA/C;AACD,OANM,MAMA,IACL,CAACjB,KAAK,CAACQ,KAAN,KAAgBnB,KAAK,CAAC6B,MAAtB,IAAgClB,KAAK,CAACQ,KAAN,KAAgBnB,KAAK,CAAC8B,SAAvD,KACAnB,KAAK,CAACC,QAAN,KAAmBD,KAAK,CAACQ,KAFpB,EAGL;AAAA;;AACA,YAAIR,KAAK,CAACC,QAAN,KAAmBZ,KAAK,CAACuB,MAA7B,EAAqC;AAAA;;AACnC,0DAAAP,OAAO,CAACK,QAAR,EAAiBK,KAAjB,2GAAyBf,KAAzB,EAAgC,KAAhC;AACD;;AACD,wDAAAK,OAAO,CAACK,QAAR,EAAiBM,UAAjB,2GAA8BhB,KAA9B,EAAqC,KAArC;AACAJ,QAAAA,oBAAoB,CAACwB,MAArB,CAA4BpB,KAAK,CAACM,UAAlC;AACAR,QAAAA,eAAe,CAACO,OAAO,CAACK,QAAR,CAAiBJ,UAAlB,CAAf,GAA+CW,SAA/C;AACD;AACF,KA9BD,MA8BO,IAAIf,YAAY,CAACF,KAAD,CAAhB,EAAyB;AAC9B,UAAI,CAACJ,oBAAoB,CAACyB,GAArB,CAAyBrB,KAAK,CAACM,UAA/B,CAAL,EAAiD;AAC/CV,QAAAA,oBAAoB,CAAC0B,GAArB,CACEtB,KAAK,CAACM,UADR,EAEEb,mBAAmB,CAAC8B,MAApB,CAA2BvB,KAAK,CAACM,UAAjC,CAFF;AAID,OAN6B,CAQ9B;;;AACA,YAAMkB,OAAO,GAAG5B,oBAAoB,CAAC6B,GAArB,CAAyBzB,KAAK,CAACM,UAA/B,CAAhB;;AAEA,cAAQN,KAAK,CAACG,SAAd;AACE,aAAKb,cAAc,CAACoC,YAApB;AACE,gCAAArB,OAAO,CAACK,QAAR,mGAAkBiB,aAAlB,yGAAkC3B,KAAlC,EAAyCwB,OAAzC;AACA;;AACF,aAAKlC,cAAc,CAACsC,YAApB;AACE,gCAAAvB,OAAO,CAACK,QAAR,mGAAkBmB,aAAlB,yGAAkC7B,KAAlC,EAAyCwB,OAAzC;AACA;;AACF,aAAKlC,cAAc,CAACwC,UAApB;AACE,gCAAAzB,OAAO,CAACK,QAAR,mGAAkBqB,WAAlB,yGAAgC/B,KAAhC,EAAuCwB,OAAvC;AACA;;AACF,aAAKlC,cAAc,CAAC0C,iBAApB;AACE,iCAAA3B,OAAO,CAACK,QAAR,qGAAkBuB,kBAAlB,0GAAuCjC,KAAvC,EAA8CwB,OAA9C;AACA;AAZJ;AAcD,KAzBM,MAyBA;AAAA;;AACL,sDAAAnB,OAAO,CAACK,QAAR,EAAiBwB,QAAjB,0GAA4BlC,KAA5B;;AAEA,UAAIK,OAAO,CAACK,QAAR,CAAiByB,QAAjB,IAA6B9B,OAAO,CAACK,QAAR,CAAiB0B,qBAAlD,EAAyE;AAAA;;AACvE,wDAAA/B,OAAO,CAACK,QAAR,EAAiByB,QAAjB,mIACE,uBAAA9B,OAAO,CAACK,QAAR,EAAiB0B,qBADnB,0DACE,gDACEpC,KADF,EAEEF,eAAe,CAACO,OAAO,CAACK,QAAR,CAAiBJ,UAAlB,CAFjB,CADF;AAOAR,QAAAA,eAAe,CAACO,OAAO,CAACK,QAAR,CAAiBJ,UAAlB,CAAf,GAA+CN,KAA/C;AACD;AACF;AACF,GAtED,MAsEO;AACL,UAAMqC,UAAU,GAAG7C,qBAAqB,CAACQ,KAAK,CAACM,UAAP,CAAxC;;AACA,QAAI+B,UAAJ,EAAgB;AACd,YAAMC,WAAW,GAAG;AAAEA,QAAAA,WAAW,EAAEtC;AAAf,OAApB;;AACA,UAAID,kBAAkB,CAACC,KAAD,CAAtB,EAA+B;AAC7BqC,QAAAA,UAAU,CAACE,oBAAX,CAAgCD,WAAhC;AACD,OAFD,MAEO;AACLD,QAAAA,UAAU,CAACG,cAAX,CAA0BF,WAA1B;AACD;;AACD;AACD;AACF;AACF;AAED,OAAO,SAASG,cAAT,GAA0B;AAC/BC,EAAAA,aAAa;AAEbhD,EAAAA,+BAA+B,GAAGN,kBAAkB,CAACuD,WAAnB,CAChC,uBADgC,EAEhCvC,qBAFgC,CAAlC;AAKAT,EAAAA,0CAA0C,GAAGP,kBAAkB,CAACuD,WAAnB,CAC3C,6BAD2C,EAE3CvC,qBAF2C,CAA7C;AAID;AAED,OAAO,SAASsC,aAAT,GAAyB;AAC9B,MAAIhD,+BAAJ,EAAqC;AACnCA,IAAAA,+BAA+B,CAACkD,MAAhC;AACAlD,IAAAA,+BAA+B,GAAG,IAAlC;AACD;;AAED,MAAIC,0CAAJ,EAAgD;AAC9CA,IAAAA,0CAA0C,CAACiD,MAA3C;AACAjD,IAAAA,0CAA0C,GAAG,IAA7C;AACD;AACF", "sourcesContent": ["import { DeviceEventEmitter, EmitterSubscription } from 'react-native';\nimport { State } from '../../State';\nimport { TouchEventType } from '../../TouchEventType';\nimport {\n  GestureTouchEvent,\n  GestureUpdateEvent,\n  GestureStateChangeEvent,\n} from '../gestureHandlerCommon';\nimport { findHandler, findOldGestureHandler } from '../handlersRegistry';\nimport { BaseGesture } from './gesture';\nimport {\n  GestureStateManager,\n  GestureStateManagerType,\n} from './gestureStateManager';\n\nlet gestureHandlerEventSubscription: EmitterSubscription | null = null;\nlet gestureHandlerStateChangeEventSubscription: EmitterSubscription | null =\n  null;\n\nconst gestureStateManagers: Map<number, GestureStateManagerType> = new Map<\n  number,\n  GestureStateManagerType\n>();\n\nconst lastUpdateEvent: (GestureUpdateEvent | undefined)[] = [];\n\nfunction isStateChangeEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n): event is GestureStateChangeEvent {\n  // @ts-ignore oldState doesn't exist on GestureTouchEvent and that's the point\n  return event.oldState != null;\n}\n\nfunction isTouchEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n): event is GestureTouchEvent {\n  return event.eventType != null;\n}\n\nexport function onGestureHandlerEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n) {\n  const handler = findHandler(event.handlerTag) as BaseGesture<\n    Record<string, unknown>\n  >;\n\n  if (handler) {\n    if (isStateChangeEvent(event)) {\n      if (\n        event.oldState === State.UNDETERMINED &&\n        event.state === State.BEGAN\n      ) {\n        handler.handlers.onBegin?.(event);\n      } else if (\n        (event.oldState === State.BEGAN ||\n          event.oldState === State.UNDETERMINED) &&\n        event.state === State.ACTIVE\n      ) {\n        handler.handlers.onStart?.(event);\n        lastUpdateEvent[handler.handlers.handlerTag] = event;\n      } else if (event.oldState !== event.state && event.state === State.END) {\n        if (event.oldState === State.ACTIVE) {\n          handler.handlers.onEnd?.(event, true);\n        }\n        handler.handlers.onFinalize?.(event, true);\n        lastUpdateEvent[handler.handlers.handlerTag] = undefined;\n      } else if (\n        (event.state === State.FAILED || event.state === State.CANCELLED) &&\n        event.oldState !== event.state\n      ) {\n        if (event.oldState === State.ACTIVE) {\n          handler.handlers.onEnd?.(event, false);\n        }\n        handler.handlers.onFinalize?.(event, false);\n        gestureStateManagers.delete(event.handlerTag);\n        lastUpdateEvent[handler.handlers.handlerTag] = undefined;\n      }\n    } else if (isTouchEvent(event)) {\n      if (!gestureStateManagers.has(event.handlerTag)) {\n        gestureStateManagers.set(\n          event.handlerTag,\n          GestureStateManager.create(event.handlerTag)\n        );\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const manager = gestureStateManagers.get(event.handlerTag)!;\n\n      switch (event.eventType) {\n        case TouchEventType.TOUCHES_DOWN:\n          handler.handlers?.onTouchesDown?.(event, manager);\n          break;\n        case TouchEventType.TOUCHES_MOVE:\n          handler.handlers?.onTouchesMove?.(event, manager);\n          break;\n        case TouchEventType.TOUCHES_UP:\n          handler.handlers?.onTouchesUp?.(event, manager);\n          break;\n        case TouchEventType.TOUCHES_CANCELLED:\n          handler.handlers?.onTouchesCancelled?.(event, manager);\n          break;\n      }\n    } else {\n      handler.handlers.onUpdate?.(event);\n\n      if (handler.handlers.onChange && handler.handlers.changeEventCalculator) {\n        handler.handlers.onChange?.(\n          handler.handlers.changeEventCalculator?.(\n            event,\n            lastUpdateEvent[handler.handlers.handlerTag]\n          )\n        );\n\n        lastUpdateEvent[handler.handlers.handlerTag] = event;\n      }\n    }\n  } else {\n    const oldHandler = findOldGestureHandler(event.handlerTag);\n    if (oldHandler) {\n      const nativeEvent = { nativeEvent: event };\n      if (isStateChangeEvent(event)) {\n        oldHandler.onGestureStateChange(nativeEvent);\n      } else {\n        oldHandler.onGestureEvent(nativeEvent);\n      }\n      return;\n    }\n  }\n}\n\nexport function startListening() {\n  stopListening();\n\n  gestureHandlerEventSubscription = DeviceEventEmitter.addListener(\n    'onGestureHandlerEvent',\n    onGestureHandlerEvent\n  );\n\n  gestureHandlerStateChangeEventSubscription = DeviceEventEmitter.addListener(\n    'onGestureHandlerStateChange',\n    onGestureHandlerEvent\n  );\n}\n\nexport function stopListening() {\n  if (gestureHandlerEventSubscription) {\n    gestureHandlerEventSubscription.remove();\n    gestureHandlerEventSubscription = null;\n  }\n\n  if (gestureHandlerStateChangeEventSubscription) {\n    gestureHandlerStateChangeEventSubscription.remove();\n    gestureHandlerStateChangeEventSubscription = null;\n  }\n}\n"]}