import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  FlatList,
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import {
  BorderRadius,
  FontSizes,
  Spacing,
  TunaWorkColors,
} from "../../src/constants";
import { useTranslation } from "../../src/i18n";

interface ChatMessage {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  isOnline: boolean;
  isTyping?: boolean;
  projectTitle?: string;
  messageType: "text" | "file" | "image";
}

const mockChats: ChatMessage[] = [
  {
    id: "1",
    senderId: "user1",
    senderName: "<PERSON>",
    senderAvatar:
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
    lastMessage: "Parfait ! Je vais commencer le design dès demain matin.",
    timestamp: "2 min",
    unreadCount: 2,
    isOnline: true,
    projectTitle: "Design d'application mobile",
    messageType: "text",
  },
  {
    id: "2",
    senderId: "user2",
    senderName: "Jean-Claude Mukendi",
    senderAvatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
    lastMessage: "Voici les fichiers que vous avez demandés",
    timestamp: "15 min",
    unreadCount: 0,
    isOnline: true,
    isTyping: true,
    projectTitle: "Développement site web",
    messageType: "file",
  },
  {
    id: "3",
    senderId: "user3",
    senderName: "Grace Mbuyi",
    senderAvatar:
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
    lastMessage: "Merci pour votre retour, je vais ajuster le contenu.",
    timestamp: "1h",
    unreadCount: 1,
    isOnline: false,
    projectTitle: "Rédaction articles blog",
    messageType: "text",
  },
  {
    id: "4",
    senderId: "user4",
    senderName: "Patrick Tshisekedi",
    senderAvatar:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    lastMessage: "Photo envoyée",
    timestamp: "3h",
    unreadCount: 0,
    isOnline: false,
    projectTitle: "Campagne marketing",
    messageType: "image",
  },
];

export default function MessagesScreen() {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<"all" | "unread" | "archived">(
    "all"
  );

  const filteredChats = mockChats.filter(
    (chat) =>
      chat.senderName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      chat.projectTitle?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getMessageIcon = (type: string) => {
    switch (type) {
      case "file":
        return "document-attach";
      case "image":
        return "image";
      default:
        return null;
    }
  };

  const renderChatItem = ({ item }: { item: ChatMessage }) => (
    <TouchableOpacity
      style={styles.chatItem}
      onPress={() => router.push(`/chat/${item.id}` as any)}
    >
      <View style={styles.avatarContainer}>
        <Image source={{ uri: item.senderAvatar }} style={styles.avatar} />
        <View
          style={[
            styles.onlineIndicator,
            { backgroundColor: item.isOnline ? "#10B981" : "#6B7280" },
          ]}
        />
        {item.unreadCount > 0 && (
          <View style={styles.unreadBadge}>
            <Text style={styles.unreadText}>{item.unreadCount}</Text>
          </View>
        )}
      </View>

      <View style={styles.chatContent}>
        <View style={styles.chatHeader}>
          <Text style={styles.senderName}>{item.senderName}</Text>
          <Text style={styles.timestamp}>{item.timestamp}</Text>
        </View>

        {item.projectTitle && (
          <Text style={styles.projectTitle}>{item.projectTitle}</Text>
        )}

        <View style={styles.lastMessageContainer}>
          {getMessageIcon(item.messageType) && (
            <Ionicons
              name={getMessageIcon(item.messageType) as any}
              size={16}
              color={TunaWorkColors.secondary[500]}
              style={styles.messageIcon}
            />
          )}
          <Text
            style={[
              styles.lastMessage,
              item.unreadCount > 0 && styles.unreadMessage,
            ]}
          >
            {item.isTyping ? "En train d'écrire..." : item.lastMessage}
          </Text>
        </View>
      </View>

      <View style={styles.chatActions}>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="call" size={20} color={TunaWorkColors.primary[500]} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons
            name="videocam"
            size={20}
            color={TunaWorkColors.primary[500]}
          />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.headerTitle}>Messages</Text>
          <Text style={styles.headerSubtitle}>
            {filteredChats.length} conversations
          </Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons
              name="search"
              size={24}
              color={TunaWorkColors.secondary[600]}
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons
              name="add"
              size={24}
              color={TunaWorkColors.secondary[600]}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <Ionicons
            name="search"
            size={20}
            color={TunaWorkColors.secondary[400]}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher une conversation..."
            placeholderTextColor={TunaWorkColors.secondary[400]}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        {(["all", "unread", "archived"] as const).map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[styles.tab, activeTab === tab && styles.activeTab]}
            onPress={() => setActiveTab(tab)}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === tab && styles.activeTabText,
              ]}
            >
              {tab === "all"
                ? "Toutes"
                : tab === "unread"
                ? "Non lues"
                : "Archivées"}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Chat List */}
      <FlatList
        data={filteredChats}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.chatList}
      />

      {/* Floating Action Button */}
      <TouchableOpacity style={styles.fab}>
        <Ionicons name="chatbubble" size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F8FAFC",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: TunaWorkColors.secondary[100],
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: FontSizes["2xl"],
    fontWeight: "700",
    color: TunaWorkColors.secondary[900],
  },
  headerSubtitle: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[500],
    marginTop: 2,
  },
  headerRight: {
    flexDirection: "row",
    gap: Spacing.sm,
  },
  headerButton: {
    padding: Spacing.sm,
    backgroundColor: TunaWorkColors.secondary[50],
    borderRadius: BorderRadius.lg,
  },
  searchSection: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    backgroundColor: "#FFFFFF",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: TunaWorkColors.secondary[50],
    borderRadius: BorderRadius.xl,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    gap: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[900],
  },
  tabsContainer: {
    flexDirection: "row",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: Spacing.md,
    paddingBottom: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: TunaWorkColors.secondary[100],
  },
  tab: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.sm,
    borderRadius: BorderRadius.lg,
  },
  activeTab: {
    backgroundColor: TunaWorkColors.primary[500],
  },
  tabText: {
    fontSize: FontSizes.sm,
    fontWeight: "500",
    color: TunaWorkColors.secondary[600],
  },
  activeTabText: {
    color: "#FFFFFF",
  },
  chatList: {
    paddingVertical: Spacing.sm,
  },
  chatItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    backgroundColor: "#FFFFFF",
    marginHorizontal: Spacing.md,
    marginVertical: Spacing.xs,
    borderRadius: BorderRadius.xl,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  avatarContainer: {
    position: "relative",
    marginRight: Spacing.md,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    borderWidth: 2,
    borderColor: "#FFFFFF",
  },
  onlineIndicator: {
    position: "absolute",
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: "#FFFFFF",
  },
  unreadBadge: {
    position: "absolute",
    top: -4,
    right: -4,
    backgroundColor: "#EF4444",
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#FFFFFF",
  },
  unreadText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
  chatContent: {
    flex: 1,
    marginRight: Spacing.sm,
  },
  chatHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 2,
  },
  senderName: {
    fontSize: FontSizes.lg,
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
  },
  timestamp: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.secondary[500],
  },
  projectTitle: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.primary[500],
    fontWeight: "500",
    marginBottom: 4,
  },
  lastMessageContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  messageIcon: {
    marginRight: 4,
  },
  lastMessage: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
    flex: 1,
  },
  unreadMessage: {
    fontWeight: "600",
    color: TunaWorkColors.secondary[900],
  },
  chatActions: {
    flexDirection: "row",
    gap: Spacing.xs,
  },
  actionButton: {
    padding: Spacing.sm,
    backgroundColor: TunaWorkColors.primary[50],
    borderRadius: BorderRadius.lg,
  },
  fab: {
    position: "absolute",
    bottom: Spacing.lg,
    right: Spacing.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: TunaWorkColors.primary[500],
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});
