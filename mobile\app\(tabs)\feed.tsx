import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { useTranslation } from '../../src/i18n';
import { Card } from '../../src/components/ui';
import { TunaWorkColors, Spacing, FontSizes, BorderRadius } from '../../src/constants';
import { feedPosts, FeedPost } from '../../src/lib/data';

export default function FeedScreen() {
  const { t } = useTranslation();
  const [activeFilter, setActiveFilter] = useState('all');

  const filters = [
    { key: 'all', label: t('feed.all'), icon: 'grid' },
    { key: 'projects', label: t('feed.projects'), icon: 'briefcase' },
    { key: 'announcements', label: t('feed.announcements'), icon: 'megaphone' },
    { key: 'tips', label: t('feed.tips'), icon: 'lightbulb' },
  ];

  const filteredPosts = feedPosts.filter(post => 
    activeFilter === 'all' || 
    (activeFilter === 'projects' && post.type === 'project') ||
    (activeFilter === 'announcements' && post.type === 'announcement') ||
    (activeFilter === 'tips' && post.type === 'tip')
  );

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const postTime = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - postTime.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Il y a moins d\'1h';
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `Il y a ${diffInDays}j`;
  };

  const getPostTypeColor = (type: FeedPost['type']) => {
    switch (type) {
      case 'project': return TunaWorkColors.primary[500];
      case 'announcement': return TunaWorkColors.accent[500];
      case 'tip': return '#F59E0B';
      case 'success_story': return '#10B981';
      default: return TunaWorkColors.secondary[500];
    }
  };

  const getPostTypeIcon = (type: FeedPost['type']) => {
    switch (type) {
      case 'project': return 'briefcase';
      case 'announcement': return 'megaphone';
      case 'tip': return 'lightbulb';
      case 'success_story': return 'trophy';
      default: return 'document';
    }
  };

  const renderPost = ({ item }: { item: FeedPost }) => (
    <Card style={styles.postCard} padding="md">
      {/* Post Header */}
      <View style={styles.postHeader}>
        <View style={styles.authorInfo}>
          <Image source={{ uri: item.author.avatar }} style={styles.authorAvatar} />
          <View style={styles.authorDetails}>
            <Text style={styles.authorName}>{item.author.name}</Text>
            <Text style={styles.authorTitle}>{item.author.title}</Text>
          </View>
        </View>
        <View style={styles.postMeta}>
          <View style={[styles.postTypeBadge, { backgroundColor: getPostTypeColor(item.type) }]}>
            <Ionicons 
              name={getPostTypeIcon(item.type) as any} 
              size={12} 
              color="#FFFFFF" 
            />
          </View>
          <Text style={styles.timestamp}>{formatTimeAgo(item.timestamp)}</Text>
        </View>
      </View>

      {/* Post Content */}
      <Text style={styles.postTitle}>{item.title}</Text>
      <Text style={styles.postContent} numberOfLines={3}>
        {item.content}
      </Text>

      {/* Post Image */}
      {item.image && (
        <Image source={{ uri: item.image }} style={styles.postImage} />
      )}

      {/* Project Details */}
      {item.type === 'project' && item.budget && (
        <View style={styles.projectDetails}>
          <View style={styles.budgetContainer}>
            <Ionicons name="cash" size={16} color={TunaWorkColors.accent[500]} />
            <Text style={styles.budgetText}>
              ${item.budget.min} - ${item.budget.max} {item.budget.currency}
            </Text>
          </View>
          {item.deadline && (
            <View style={styles.deadlineContainer}>
              <Ionicons name="calendar" size={16} color={TunaWorkColors.secondary[500]} />
              <Text style={styles.deadlineText}>
                Échéance: {new Date(item.deadline).toLocaleDateString('fr-FR')}
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Skills Tags */}
      {item.skills && (
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.skillsContainer}>
          {item.skills.map((skill, index) => (
            <View key={index} style={styles.skillTag}>
              <Text style={styles.skillText}>{skill}</Text>
            </View>
          ))}
        </ScrollView>
      )}

      {/* Post Actions */}
      <View style={styles.postActions}>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons 
            name={item.isLiked ? "heart" : "heart-outline"} 
            size={20} 
            color={item.isLiked ? "#EF4444" : TunaWorkColors.secondary[500]} 
          />
          <Text style={styles.actionText}>{item.likes}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="chatbubble-outline" size={20} color={TunaWorkColors.secondary[500]} />
          <Text style={styles.actionText}>{item.comments}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="share-outline" size={20} color={TunaWorkColors.secondary[500]} />
          <Text style={styles.actionText}>{item.shares}</Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{t('feed.title')}</Text>
        <TouchableOpacity style={styles.searchButton}>
          <Ionicons name="search" size={24} color={TunaWorkColors.secondary[600]} />
        </TouchableOpacity>
      </View>

      {/* Filters */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false} 
        style={styles.filtersContainer}
        contentContainerStyle={styles.filtersContent}
      >
        {filters.map((filter) => (
          <TouchableOpacity
            key={filter.key}
            style={[
              styles.filterButton,
              activeFilter === filter.key && styles.activeFilterButton
            ]}
            onPress={() => setActiveFilter(filter.key)}
          >
            <Ionicons 
              name={filter.icon as any} 
              size={16} 
              color={activeFilter === filter.key ? '#FFFFFF' : TunaWorkColors.secondary[600]} 
            />
            <Text style={[
              styles.filterText,
              activeFilter === filter.key && styles.activeFilterText
            ]}>
              {filter.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Posts List */}
      <FlatList
        data={filteredPosts}
        renderItem={renderPost}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.postsList}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: TunaWorkColors.secondary[50],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    backgroundColor: '#FFFFFF',
  },
  headerTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: '700',
    color: TunaWorkColors.secondary[900],
  },
  searchButton: {
    padding: Spacing.sm,
  },
  filtersContainer: {
    backgroundColor: '#FFFFFF',
    paddingBottom: Spacing.sm,
  },
  filtersContent: {
    paddingHorizontal: Spacing.md,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
    backgroundColor: TunaWorkColors.secondary[100],
    marginRight: Spacing.sm,
    gap: Spacing.xs,
  },
  activeFilterButton: {
    backgroundColor: TunaWorkColors.primary[500],
  },
  filterText: {
    fontSize: FontSizes.sm,
    fontWeight: '500',
    color: TunaWorkColors.secondary[600],
  },
  activeFilterText: {
    color: '#FFFFFF',
  },
  postsList: {
    padding: Spacing.md,
  },
  postCard: {
    marginBottom: Spacing.md,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  authorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  authorAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: Spacing.sm,
  },
  authorDetails: {
    flex: 1,
  },
  authorName: {
    fontSize: FontSizes.base,
    fontWeight: '600',
    color: TunaWorkColors.secondary[900],
  },
  authorTitle: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
  },
  postMeta: {
    alignItems: 'flex-end',
  },
  postTypeBadge: {
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
    marginBottom: 4,
  },
  timestamp: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.secondary[500],
  },
  postTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: TunaWorkColors.secondary[900],
    marginBottom: Spacing.xs,
  },
  postContent: {
    fontSize: FontSizes.base,
    color: TunaWorkColors.secondary[700],
    lineHeight: 22,
    marginBottom: Spacing.sm,
  },
  postImage: {
    width: '100%',
    height: 200,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.sm,
  },
  projectDetails: {
    backgroundColor: TunaWorkColors.secondary[50],
    padding: Spacing.sm,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.sm,
  },
  budgetContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  budgetText: {
    fontSize: FontSizes.sm,
    fontWeight: '600',
    color: TunaWorkColors.accent[600],
    marginLeft: Spacing.xs,
  },
  deadlineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deadlineText: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
    marginLeft: Spacing.xs,
  },
  skillsContainer: {
    marginBottom: Spacing.sm,
  },
  skillTag: {
    backgroundColor: TunaWorkColors.primary[100],
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
    marginRight: Spacing.xs,
  },
  skillText: {
    fontSize: FontSizes.xs,
    color: TunaWorkColors.primary[700],
    fontWeight: '500',
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: TunaWorkColors.secondary[200],
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  actionText: {
    fontSize: FontSizes.sm,
    color: TunaWorkColors.secondary[600],
  },
});
