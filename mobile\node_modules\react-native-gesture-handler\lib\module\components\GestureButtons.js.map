{"version": 3, "sources": ["GestureButtons.tsx"], "names": ["React", "Animated", "Platform", "processColor", "StyleSheet", "createNativeWrapper", "GestureHandlerButton", "State", "isF<PERSON><PERSON>", "RawButton", "shouldCancelWhenOutside", "shouldActivateOnStart", "IS_FABRIC", "InnerBaseButton", "Component", "constructor", "props", "nativeEvent", "state", "oldState", "pointerInside", "active", "ACTIVE", "lastActive", "onActiveStateChange", "longPressDetected", "CANCELLED", "onPress", "OS", "BEGAN", "onLongPress", "longPressTimeout", "setTimeout", "delayLongPress", "undefined", "clearTimeout", "END", "FAILED", "e", "onHandlerStateChange", "handleEvent", "onGestureEvent", "render", "rippleColor", "unprocessedRippleColor", "style", "rest", "innerRef", "cursor", "BaseButton", "forwardRef", "ref", "AnimatedBaseButton", "createAnimatedComponent", "btnStyles", "create", "underlay", "position", "left", "right", "bottom", "top", "InnerRectButton", "opacity", "setValue", "activeOpacity", "Value", "children", "resolvedStyle", "flatten", "backgroundColor", "underlayColor", "borderRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "RectButton", "InnerBorderlessButton", "borderless", "BorderlessButton", "default", "PureNativeButton"], "mappings": ";;;;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AACA,SAASC,QAAT,EAAmBC,QAAnB,EAA6BC,YAA7B,EAA2CC,UAA3C,QAA6D,cAA7D;AAEA,OAAOC,mBAAP,MAAgC,iCAAhC;AACA,OAAOC,oBAAP,MAAiC,wBAAjC;AACA,SAASC,KAAT,QAAsB,UAAtB;AAeA,SAASC,QAAT,QAAyB,UAAzB;AAEA,OAAO,MAAMC,SAAS,GAAGJ,mBAAmB,CAACC,oBAAD,EAAuB;AACjEI,EAAAA,uBAAuB,EAAE,KADwC;AAEjEC,EAAAA,qBAAqB,EAAE;AAF0C,CAAvB,CAArC;AAKP,IAAIC,SAAyB,GAAG,IAAhC;;AAEA,MAAMC,eAAN,SAA8Bb,KAAK,CAACc,SAApC,CAAsE;AASpEC,EAAAA,WAAW,CAACC,KAAD,EAAyB;AAClC,UAAMA,KAAN;;AADkC;;AAAA;;AAAA;;AAAA,yCAMd,CAAC;AACrBC,MAAAA;AADqB,KAAD,KAE0C;AAC9D,YAAM;AAAEC,QAAAA,KAAF;AAASC,QAAAA,QAAT;AAAmBC,QAAAA;AAAnB,UAAqCH,WAA3C;AACA,YAAMI,MAAM,GAAGD,aAAa,IAAIF,KAAK,KAAKX,KAAK,CAACe,MAAhD;;AAEA,UAAID,MAAM,KAAK,KAAKE,UAAhB,IAA8B,KAAKP,KAAL,CAAWQ,mBAA7C,EAAkE;AAChE,aAAKR,KAAL,CAAWQ,mBAAX,CAA+BH,MAA/B;AACD;;AAED,UACE,CAAC,KAAKI,iBAAN,IACAN,QAAQ,KAAKZ,KAAK,CAACe,MADnB,IAEAJ,KAAK,KAAKX,KAAK,CAACmB,SAFhB,IAGA,KAAKH,UAHL,IAIA,KAAKP,KAAL,CAAWW,OALb,EAME;AACA,aAAKX,KAAL,CAAWW,OAAX,CAAmBP,aAAnB;AACD;;AAED,UACE,CAAC,KAAKG,UAAN,IACA;AACAL,MAAAA,KAAK,MAAMhB,QAAQ,CAAC0B,EAAT,KAAgB,SAAhB,GAA4BrB,KAAK,CAACe,MAAlC,GAA2Cf,KAAK,CAACsB,KAAvD,CAFL,IAGAT,aAJF,EAKE;AACA,aAAKK,iBAAL,GAAyB,KAAzB;;AACA,YAAI,KAAKT,KAAL,CAAWc,WAAf,EAA4B;AAC1B,eAAKC,gBAAL,GAAwBC,UAAU,CAChC,KAAKF,WAD2B,EAEhC,KAAKd,KAAL,CAAWiB,cAFqB,CAAlC;AAID;AACF,OAbD,MAaO,KACL;AACAf,MAAAA,KAAK,KAAKX,KAAK,CAACe,MAAhB,IACA,CAACF,aADD,IAEA,KAAKW,gBAAL,KAA0BG,SAJrB,EAKL;AACAC,QAAAA,YAAY,CAAC,KAAKJ,gBAAN,CAAZ;AACA,aAAKA,gBAAL,GAAwBG,SAAxB;AACD,OARM,MAQA,KACL;AACA,WAAKH,gBAAL,KAA0BG,SAA1B,KACChB,KAAK,KAAKX,KAAK,CAAC6B,GAAhB,IACClB,KAAK,KAAKX,KAAK,CAACmB,SADjB,IAECR,KAAK,KAAKX,KAAK,CAAC8B,MAHlB,CAFK,EAML;AACAF,QAAAA,YAAY,CAAC,KAAKJ,gBAAN,CAAZ;AACA,aAAKA,gBAAL,GAAwBG,SAAxB;AACD;;AAED,WAAKX,UAAL,GAAkBF,MAAlB;AACD,KA3DmC;;AAAA,yCA6Dd,MAAM;AAAA;;AAC1B,WAAKI,iBAAL,GAAyB,IAAzB;AACA,mDAAKT,KAAL,EAAWc,WAAX;AACD,KAhEmC;;AAAA,kDAuElCQ,CAD6B,IAE1B;AAAA;;AACH,oDAAKtB,KAAL,EAAWuB,oBAAX,mGAAkCD,CAAlC;AACA,WAAKE,WAAL,CAAiBF,CAAjB;AACD,KA3EmC;;AAAA,4CA8ElCA,CADuB,IAEpB;AAAA;;AACH,oDAAKtB,KAAL,EAAWyB,cAAX,mGAA4BH,CAA5B;AACA,WAAKE,WAAL,CACEF,CADF,EAFG,CAIA;AACJ,KApFmC;;AAElC,SAAKf,UAAL,GAAkB,KAAlB;AACA,SAAKE,iBAAL,GAAyB,KAAzB;AACD;;AAkFDiB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEC,MAAAA,WAAW,EAAEC,sBAAf;AAAuCC,MAAAA,KAAvC;AAA8C,SAAGC;AAAjD,QAA0D,KAAK9B,KAArE;;AAEA,QAAIJ,SAAS,KAAK,IAAlB,EAAwB;AACtBA,MAAAA,SAAS,GAAGJ,QAAQ,EAApB;AACD;;AAED,UAAMmC,WAAW,GAAG/B,SAAS,GACzBgC,sBADyB,GAEzBzC,YAAY,CAACyC,sBAAD,aAACA,sBAAD,cAACA,sBAAD,GAA2BV,SAA3B,CAFhB;AAIA,wBACE,oBAAC,SAAD;AACE,MAAA,GAAG,EAAE,KAAKlB,KAAL,CAAW+B,QADlB;AAEE,MAAA,WAAW,EAAEJ,WAFf;AAGE,MAAA,KAAK,EAAE,CAACE,KAAD,EAAQ3C,QAAQ,CAAC0B,EAAT,KAAgB,KAAhB,IAAyB;AAAEoB,QAAAA,MAAM,EAAEd;AAAV,OAAjC;AAHT,OAIMY,IAJN;AAKE,MAAA,cAAc,EAAE,KAAKL,cALvB;AAME,MAAA,oBAAoB,EAAE,KAAKF;AAN7B,OADF;AAUD;;AApHmE;;gBAAhE1B,e,kBACkB;AACpBoB,EAAAA,cAAc,EAAE;AADI,C;;AAsHxB,OAAO,MAAMgB,UAAU,gBAAGjD,KAAK,CAACkD,UAAN,CAGxB,CAAClC,KAAD,EAAQmC,GAAR,kBAAgB,oBAAC,eAAD;AAAiB,EAAA,QAAQ,EAAEA;AAA3B,GAAoCnC,KAApC,EAHQ,CAAnB;AAKP,MAAMoC,kBAAkB,GAAGnD,QAAQ,CAACoD,uBAAT,CAAiCJ,UAAjC,CAA3B;AAEA,MAAMK,SAAS,GAAGlD,UAAU,CAACmD,MAAX,CAAkB;AAClCC,EAAAA,QAAQ,EAAE;AACRC,IAAAA,QAAQ,EAAE,UADF;AAERC,IAAAA,IAAI,EAAE,CAFE;AAGRC,IAAAA,KAAK,EAAE,CAHC;AAIRC,IAAAA,MAAM,EAAE,CAJA;AAKRC,IAAAA,GAAG,EAAE;AALG;AADwB,CAAlB,CAAlB;;AAUA,MAAMC,eAAN,SAA8B9D,KAAK,CAACc,SAApC,CAAsE;AAQpEC,EAAAA,WAAW,CAACC,KAAD,EAAyB;AAClC,UAAMA,KAAN;;AADkC;;AAAA,iDAKLK,MAAD,IAAqB;AAAA;;AACjD,UAAInB,QAAQ,CAAC0B,EAAT,KAAgB,SAApB,EAA+B;AAC7B,aAAKmC,OAAL,CAAaC,QAAb,CAAsB3C,MAAM,GAAG,KAAKL,KAAL,CAAWiD,aAAd,GAA+B,CAA3D;AACD;;AAED,oDAAKjD,KAAL,EAAWQ,mBAAX,mGAAiCH,MAAjC;AACD,KAXmC;;AAElC,SAAK0C,OAAL,GAAe,IAAI9D,QAAQ,CAACiE,KAAb,CAAmB,CAAnB,CAAf;AACD;;AAUDxB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEyB,MAAAA,QAAF;AAAYtB,MAAAA,KAAZ;AAAmB,SAAGC;AAAtB,QAA+B,KAAK9B,KAA1C;AAEA,UAAMoD,aAAa,GAAGhE,UAAU,CAACiE,OAAX,CAAmBxB,KAAnB,aAAmBA,KAAnB,cAAmBA,KAAnB,GAA4B,EAA5B,CAAtB;AAEA,wBACE,oBAAC,UAAD,eACMC,IADN;AAEE,MAAA,GAAG,EAAE,KAAK9B,KAAL,CAAW+B,QAFlB;AAGE,MAAA,KAAK,EAAEqB,aAHT;AAIE,MAAA,mBAAmB,EAAE,KAAK5C;AAJ5B,qBAKE,oBAAC,QAAD,CAAU,IAAV;AACE,MAAA,KAAK,EAAE,CACL8B,SAAS,CAACE,QADL,EAEL;AACEO,QAAAA,OAAO,EAAE,KAAKA,OADhB;AAEEO,QAAAA,eAAe,EAAE,KAAKtD,KAAL,CAAWuD,aAF9B;AAGEC,QAAAA,YAAY,EAAEJ,aAAa,CAACI,YAH9B;AAIEC,QAAAA,mBAAmB,EAAEL,aAAa,CAACK,mBAJrC;AAKEC,QAAAA,oBAAoB,EAAEN,aAAa,CAACM,oBALtC;AAMEC,QAAAA,sBAAsB,EAAEP,aAAa,CAACO,sBANxC;AAOEC,QAAAA,uBAAuB,EAAER,aAAa,CAACQ;AAPzC,OAFK;AADT,MALF,EAmBGT,QAnBH,CADF;AAuBD;;AAjDmE;;gBAAhEL,e,kBACkB;AACpBG,EAAAA,aAAa,EAAE,KADK;AAEpBM,EAAAA,aAAa,EAAE;AAFK,C;;AAmDxB,OAAO,MAAMM,UAAU,gBAAG7E,KAAK,CAACkD,UAAN,CAGxB,CAAClC,KAAD,EAAQmC,GAAR,kBAAgB,oBAAC,eAAD;AAAiB,EAAA,QAAQ,EAAEA;AAA3B,GAAoCnC,KAApC,EAHQ,CAAnB;;AAKP,MAAM8D,qBAAN,SAAoC9E,KAAK,CAACc,SAA1C,CAAkF;AAQhFC,EAAAA,WAAW,CAACC,KAAD,EAA+B;AACxC,UAAMA,KAAN;;AADwC;;AAAA,iDAKXK,MAAD,IAAqB;AAAA;;AACjD,UAAInB,QAAQ,CAAC0B,EAAT,KAAgB,SAApB,EAA+B;AAC7B,aAAKmC,OAAL,CAAaC,QAAb,CAAsB3C,MAAM,GAAG,KAAKL,KAAL,CAAWiD,aAAd,GAA+B,CAA3D;AACD;;AAED,qDAAKjD,KAAL,EAAWQ,mBAAX,qGAAiCH,MAAjC;AACD,KAXyC;;AAExC,SAAK0C,OAAL,GAAe,IAAI9D,QAAQ,CAACiE,KAAb,CAAmB,CAAnB,CAAf;AACD;;AAUDxB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEyB,MAAAA,QAAF;AAAYtB,MAAAA,KAAZ;AAAmBE,MAAAA,QAAnB;AAA6B,SAAGD;AAAhC,QAAyC,KAAK9B,KAApD;AAEA,wBACE,oBAAC,kBAAD,eACM8B,IADN;AAEE;AACA;AACA,MAAA,QAAQ,EAAEC,QAJZ;AAKE,MAAA,mBAAmB,EAAE,KAAKvB,mBAL5B;AAME,MAAA,KAAK,EAAE,CAACqB,KAAD,EAAQ3C,QAAQ,CAAC0B,EAAT,KAAgB,KAAhB,IAAyB;AAAEmC,QAAAA,OAAO,EAAE,KAAKA;AAAhB,OAAjC;AANT,QAOGI,QAPH,CADF;AAWD;;AAnC+E;;gBAA5EW,qB,kBACkB;AACpBb,EAAAA,aAAa,EAAE,GADK;AAEpBc,EAAAA,UAAU,EAAE;AAFQ,C;;AAqCxB,OAAO,MAAMC,gBAAgB,gBAAGhF,KAAK,CAACkD,UAAN,CAG9B,CAAClC,KAAD,EAAQmC,GAAR,kBAAgB,oBAAC,qBAAD;AAAuB,EAAA,QAAQ,EAAEA;AAAjC,GAA0CnC,KAA1C,EAHc,CAAzB;AAKP,SAASiE,OAAO,IAAIC,gBAApB,QAA4C,wBAA5C", "sourcesContent": ["import * as React from 'react';\nimport { Animated, Platform, processColor, StyleSheet } from 'react-native';\n\nimport createNativeWrapper from '../handlers/createNativeWrapper';\nimport GestureHandlerButton from './GestureHandlerButton';\nimport { State } from '../State';\n\nimport {\n  GestureEvent,\n  HandlerStateChangeEvent,\n} from '../handlers/gestureHandlerCommon';\nimport type { NativeViewGestureHandlerPayload } from '../handlers/GestureHandlerEventPayload';\nimport type {\n  BaseButtonWithRefProps,\n  BaseButtonProps,\n  RectButtonWithRefProps,\n  RectButtonProps,\n  BorderlessButtonWithRefProps,\n  BorderlessButtonProps,\n} from './GestureButtonsProps';\nimport { isFabric } from '../utils';\n\nexport const RawButton = createNativeWrapper(GestureHandlerButton, {\n  shouldCancelWhenOutside: false,\n  shouldActivateOnStart: false,\n});\n\nlet IS_FABRIC: null | boolean = null;\n\nclass InnerBaseButton extends React.Component<BaseButtonWithRefProps> {\n  static defaultProps = {\n    delayLongPress: 600,\n  };\n\n  private lastActive: boolean;\n  private longPressTimeout: ReturnType<typeof setTimeout> | undefined;\n  private longPressDetected: boolean;\n\n  constructor(props: BaseButtonProps) {\n    super(props);\n    this.lastActive = false;\n    this.longPressDetected = false;\n  }\n\n  private handleEvent = ({\n    nativeEvent,\n  }: HandlerStateChangeEvent<NativeViewGestureHandlerPayload>) => {\n    const { state, oldState, pointerInside } = nativeEvent;\n    const active = pointerInside && state === State.ACTIVE;\n\n    if (active !== this.lastActive && this.props.onActiveStateChange) {\n      this.props.onActiveStateChange(active);\n    }\n\n    if (\n      !this.longPressDetected &&\n      oldState === State.ACTIVE &&\n      state !== State.CANCELLED &&\n      this.lastActive &&\n      this.props.onPress\n    ) {\n      this.props.onPress(pointerInside);\n    }\n\n    if (\n      !this.lastActive &&\n      // NativeViewGestureHandler sends different events based on platform\n      state === (Platform.OS !== 'android' ? State.ACTIVE : State.BEGAN) &&\n      pointerInside\n    ) {\n      this.longPressDetected = false;\n      if (this.props.onLongPress) {\n        this.longPressTimeout = setTimeout(\n          this.onLongPress,\n          this.props.delayLongPress\n        );\n      }\n    } else if (\n      // Cancel longpress timeout if it's set and the finger moved out of the view\n      state === State.ACTIVE &&\n      !pointerInside &&\n      this.longPressTimeout !== undefined\n    ) {\n      clearTimeout(this.longPressTimeout);\n      this.longPressTimeout = undefined;\n    } else if (\n      // Cancel longpress timeout if it's set and the gesture has finished\n      this.longPressTimeout !== undefined &&\n      (state === State.END ||\n        state === State.CANCELLED ||\n        state === State.FAILED)\n    ) {\n      clearTimeout(this.longPressTimeout);\n      this.longPressTimeout = undefined;\n    }\n\n    this.lastActive = active;\n  };\n\n  private onLongPress = () => {\n    this.longPressDetected = true;\n    this.props.onLongPress?.();\n  };\n\n  // Normally, the parent would execute it's handler first, then forward the\n  // event to listeners. However, here our handler is virtually only forwarding\n  // events to listeners, so we reverse the order to keep the proper order of\n  // the callbacks (from \"raw\" ones to \"processed\").\n  private onHandlerStateChange = (\n    e: HandlerStateChangeEvent<NativeViewGestureHandlerPayload>\n  ) => {\n    this.props.onHandlerStateChange?.(e);\n    this.handleEvent(e);\n  };\n\n  private onGestureEvent = (\n    e: GestureEvent<NativeViewGestureHandlerPayload>\n  ) => {\n    this.props.onGestureEvent?.(e);\n    this.handleEvent(\n      e as HandlerStateChangeEvent<NativeViewGestureHandlerPayload>\n    ); // TODO: maybe it is not correct\n  };\n\n  render() {\n    const { rippleColor: unprocessedRippleColor, style, ...rest } = this.props;\n\n    if (IS_FABRIC === null) {\n      IS_FABRIC = isFabric();\n    }\n\n    const rippleColor = IS_FABRIC\n      ? unprocessedRippleColor\n      : processColor(unprocessedRippleColor ?? undefined);\n\n    return (\n      <RawButton\n        ref={this.props.innerRef}\n        rippleColor={rippleColor}\n        style={[style, Platform.OS === 'ios' && { cursor: undefined }]}\n        {...rest}\n        onGestureEvent={this.onGestureEvent}\n        onHandlerStateChange={this.onHandlerStateChange}\n      />\n    );\n  }\n}\n\nexport const BaseButton = React.forwardRef<\n  any,\n  Omit<BaseButtonProps, 'innerRef'>\n>((props, ref) => <InnerBaseButton innerRef={ref} {...props} />);\n\nconst AnimatedBaseButton = Animated.createAnimatedComponent(BaseButton);\n\nconst btnStyles = StyleSheet.create({\n  underlay: {\n    position: 'absolute',\n    left: 0,\n    right: 0,\n    bottom: 0,\n    top: 0,\n  },\n});\n\nclass InnerRectButton extends React.Component<RectButtonWithRefProps> {\n  static defaultProps = {\n    activeOpacity: 0.105,\n    underlayColor: 'black',\n  };\n\n  private opacity: Animated.Value;\n\n  constructor(props: RectButtonProps) {\n    super(props);\n    this.opacity = new Animated.Value(0);\n  }\n\n  private onActiveStateChange = (active: boolean) => {\n    if (Platform.OS !== 'android') {\n      this.opacity.setValue(active ? this.props.activeOpacity! : 0);\n    }\n\n    this.props.onActiveStateChange?.(active);\n  };\n\n  render() {\n    const { children, style, ...rest } = this.props;\n\n    const resolvedStyle = StyleSheet.flatten(style ?? {});\n\n    return (\n      <BaseButton\n        {...rest}\n        ref={this.props.innerRef}\n        style={resolvedStyle}\n        onActiveStateChange={this.onActiveStateChange}>\n        <Animated.View\n          style={[\n            btnStyles.underlay,\n            {\n              opacity: this.opacity,\n              backgroundColor: this.props.underlayColor,\n              borderRadius: resolvedStyle.borderRadius,\n              borderTopLeftRadius: resolvedStyle.borderTopLeftRadius,\n              borderTopRightRadius: resolvedStyle.borderTopRightRadius,\n              borderBottomLeftRadius: resolvedStyle.borderBottomLeftRadius,\n              borderBottomRightRadius: resolvedStyle.borderBottomRightRadius,\n            },\n          ]}\n        />\n        {children}\n      </BaseButton>\n    );\n  }\n}\n\nexport const RectButton = React.forwardRef<\n  any,\n  Omit<RectButtonProps, 'innerRef'>\n>((props, ref) => <InnerRectButton innerRef={ref} {...props} />);\n\nclass InnerBorderlessButton extends React.Component<BorderlessButtonWithRefProps> {\n  static defaultProps = {\n    activeOpacity: 0.3,\n    borderless: true,\n  };\n\n  private opacity: Animated.Value;\n\n  constructor(props: BorderlessButtonProps) {\n    super(props);\n    this.opacity = new Animated.Value(1);\n  }\n\n  private onActiveStateChange = (active: boolean) => {\n    if (Platform.OS !== 'android') {\n      this.opacity.setValue(active ? this.props.activeOpacity! : 1);\n    }\n\n    this.props.onActiveStateChange?.(active);\n  };\n\n  render() {\n    const { children, style, innerRef, ...rest } = this.props;\n\n    return (\n      <AnimatedBaseButton\n        {...rest}\n        // @ts-ignore We don't want `innerRef` to be accessible from public API.\n        // However in this case we need to set it indirectly on `BaseButton`, hence we use ts-ignore\n        innerRef={innerRef}\n        onActiveStateChange={this.onActiveStateChange}\n        style={[style, Platform.OS === 'ios' && { opacity: this.opacity }]}>\n        {children}\n      </AnimatedBaseButton>\n    );\n  }\n}\n\nexport const BorderlessButton = React.forwardRef<\n  any,\n  Omit<BorderlessButtonProps, 'innerRef'>\n>((props, ref) => <InnerBorderlessButton innerRef={ref} {...props} />);\n\nexport { default as PureNativeButton } from './GestureHandlerButton';\n"]}