{"version": 3, "names": ["React", "Children", "processColor", "extractOpacity", "extractTransform", "units", "percentReg", "percentToFloat", "percent", "__getAnimatedValue", "matched", "match", "console", "warn", "offsetComparator", "object", "other", "extractGradient", "props", "parent", "id", "children", "gradientTransform", "transform", "gradientUnits", "stops", "<PERSON><PERSON><PERSON><PERSON>", "map", "child", "cloneElement", "l", "length", "i", "style", "offset", "stopColor", "stopOpacity", "offsetNumber", "color", "isNaN", "alpha", "Math", "round", "push", "sort", "gradient", "k", "j", "s", "name"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractGradient.ts"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,YAAY,QAAQ,cAAc;AAE3C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,gBAAgB,MAAM,oBAAoB;AAEjD,OAAOC,KAAK,MAAM,UAAU;AAE5B,MAAMC,UAAU,GAAG,6CAA6C;AAEhE,SAASC,cAAcA,CACrBC,OAKK,EACG;EACR,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAOA,OAAO;EAChB;EACA,IACE,OAAOA,OAAO,KAAK,QAAQ,IAC3B,OAAOA,OAAO,CAACC,kBAAkB,KAAK,UAAU,EAChD;IACA,OAAOD,OAAO,CAACC,kBAAkB,CAAC,CAAC;EACrC;EACA,MAAMC,OAAO,GAAG,OAAOF,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACG,KAAK,CAACL,UAAU,CAAC;EACxE,IAAI,CAACI,OAAO,EAAE;IACZE,OAAO,CAACC,IAAI,CAAC,IAAIL,OAAO,+CAA+C,CAAC;IACxE,OAAO,CAAC;EACV;EAEA,OAAOE,OAAO,CAAC,CAAC,CAAC,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC;AACrD;AAEA,MAAMI,gBAAgB,GAAGA,CAACC,MAAgB,EAAEC,KAAe,KACzDD,MAAM,CAAC,CAAC,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC;AAEtB,eAAe,SAASC,eAAeA,CACrCC,KAMkB,EAClBC,MAAe,EACf;EACA,MAAM;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,iBAAiB;IAAEC,SAAS;IAAEC;EAAc,CAAC,GAAGN,KAAK;EAC3E,IAAI,CAACE,EAAE,EAAE;IACP,OAAO,IAAI;EACb;EAEA,MAAMK,KAAK,GAAG,EAAE;EAChB,MAAMC,UAAU,GAAGL,QAAQ,GACvBpB,QAAQ,CAAC0B,GAAG,CAACN,QAAQ,EAAGO,KAAK,iBAC3B5B,KAAK,CAAC6B,YAAY,CAACD,KAAK,EAAE;IACxBT;EACF,CAAC,CACH,CAAC,GACD,EAAE;EACN,MAAMW,CAAC,GAAGJ,UAAU,CAACK,MAAM;EAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAE;IAC1B,MAAM;MACJd,KAAK,EAAE;QACLe,KAAK;QACLC,MAAM,GAAGD,KAAK,IAAIA,KAAK,CAACC,MAAM;QAC9BC,SAAS,GAAIF,KAAK,IAAIA,KAAK,CAACE,SAAS,IAAK,MAAM;QAChDC,WAAW,GAAGH,KAAK,IAAIA,KAAK,CAACG;MAC/B;IACF,CAAC,GAAGV,UAAU,CAACM,CAAC,CAAC;IACjB,MAAMK,YAAY,GAAG9B,cAAc,CAAC2B,MAAM,IAAI,CAAC,CAAC;IAChD,MAAMI,KAAK,GAAGH,SAAS,IAAIjC,YAAY,CAACiC,SAAS,CAAC;IAClD,IAAI,OAAOG,KAAK,KAAK,QAAQ,IAAIC,KAAK,CAACF,YAAY,CAAC,EAAE;MACpDzB,OAAO,CAACC,IAAI,CACV,IAAIsB,SAAS,8BAA8BD,MAAM,yBACnD,CAAC;MACD;IACF;IACA,MAAMM,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACvC,cAAc,CAACiC,WAAW,CAAC,GAAG,GAAG,CAAC;IAC3DX,KAAK,CAACkB,IAAI,CAAC,CAACN,YAAY,EAAGC,KAAK,GAAG,UAAU,GAAKE,KAAK,IAAI,EAAG,CAAC,CAAC;EAClE;EACAf,KAAK,CAACmB,IAAI,CAAC9B,gBAAgB,CAAC;EAE5B,MAAM+B,QAAQ,GAAG,EAAE;EACnB,MAAMC,CAAC,GAAGrB,KAAK,CAACM,MAAM;EACtB,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,EAAEC,CAAC,EAAE,EAAE;IAC1B,MAAMC,CAAC,GAAGvB,KAAK,CAACsB,CAAC,CAAC;IAClBF,QAAQ,CAACF,IAAI,CAACK,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;EAEA,OAAO;IACLC,IAAI,EAAE7B,EAAE;IACRyB,QAAQ;IACRxB,QAAQ,EAAEK,UAAU;IACpBF,aAAa,EAAGA,aAAa,IAAInB,KAAK,CAACmB,aAAa,CAAC,IAAK,CAAC;IAC3DF,iBAAiB,EAAElB,gBAAgB,CACjCkB,iBAAiB,IAAIC,SAAS,IAAIL,KACpC;EACF,CAAC;AACH", "ignoreList": []}