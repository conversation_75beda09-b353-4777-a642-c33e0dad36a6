"use client";

import { <PERSON> } from "@/src/i18n/routing";
import { Mail, Phone, MapPin } from "lucide-react";
import { useTranslations } from "next-intl";

export function Footer() {
  const t = useTranslations("footer");
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 rounded-lg bg-blue-500 flex items-center justify-center">
                <span className="text-white font-bold">T</span>
              </div>
              <span className="text-xl font-bold">TunaWork</span>
            </div>
            <p className="text-gray-300 mb-4">{t("company_description")}</p>
            <div className="space-y-2 text-sm text-gray-300">
              <div className="flex items-center space-x-2">
                <MapPin className="w-4 h-4" />
                <span>Kinshasa, RDC</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4" />
                <span>+*********** 789</span>
              </div>
            </div>
          </div>

          {/* Plateforme */}
          <div>
            <h3 className="font-semibold mb-4">{t("platform")}</h3>
            <ul className="space-y-2 text-sm text-gray-300">
              <li>
                <Link href="/freelancers" className="hover:text-blue-400">
                  {t("find_freelancers")}
                </Link>
              </li>
              <li>
                <Link href="/post-project" className="hover:text-blue-400">
                  {t("post_project")}
                </Link>
              </li>
              <li>
                <Link href="/how-it-works" className="hover:text-blue-400">
                  {t("how_it_works")}
                </Link>
              </li>
              <li>
                <Link href="/pricing" className="hover:text-blue-400">
                  {t("pricing")}
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="font-semibold mb-4">{t("support")}</h3>
            <ul className="space-y-2 text-sm text-gray-300">
              <li>
                <Link href="/help" className="hover:text-blue-400">
                  {t("help_center")}
                </Link>
              </li>
              <li>
                <Link href="/faq" className="hover:text-blue-400">
                  {t("faq")}
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-blue-400">
                  {t("contact_us")}
                </Link>
              </li>
              <li>
                <Link href="/become-freelancer" className="hover:text-blue-400">
                  Devenir freelancer
                </Link>
              </li>
            </ul>
          </div>

          {/* Légal */}
          <div>
            <h3 className="font-semibold mb-4">{t("legal")}</h3>
            <ul className="space-y-2 text-sm text-gray-300">
              <li>
                <Link href="/terms" className="hover:text-blue-400">
                  {t("terms")}
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="hover:text-blue-400">
                  {t("privacy")}
                </Link>
              </li>
              <li>
                <Link href="/cookies" className="hover:text-blue-400">
                  {t("cookies")}
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom */}
        <div className="border-t border-gray-700 mt-8 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">{t("copyright")}</p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <a href="#" className="text-gray-400 hover:text-blue-400">
              📘
            </a>
            <a href="#" className="text-gray-400 hover:text-blue-400">
              🐦
            </a>
            <a href="#" className="text-gray-400 hover:text-blue-400">
              💼
            </a>
            <a href="#" className="text-gray-400 hover:text-blue-400">
              📸
            </a>
            <a href="#" className="text-gray-400 hover:text-blue-400">
              💬
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}
