{"version": 3, "names": ["_utils", "require", "_WebShape", "Circle", "WebShape", "tag", "exports", "<PERSON><PERSON><PERSON><PERSON>", "Defs", "Ellipse", "FeBlend", "FeColorMatrix", "FeComponentTransfer", "FeComposite", "FeConvolveMatrix", "FeDiffuseLighting", "FeDisplacementMap", "FeDistantLight", "FeDropShadow", "FeFlood", "FeFuncA", "FeFuncB", "FeFuncG", "FeFuncR", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FeImage", "FeMerge", "FeMergeNode", "FeMorphology", "FeOffset", "FePointLight", "FeSpecularLighting", "FeSpotLight", "FeTile", "FeTurbulence", "Filter", "ForeignObject", "G", "prepareProps", "props", "x", "y", "rest", "translate", "Image", "Line", "LinearGradient", "<PERSON><PERSON>", "Mask", "Path", "Pattern", "Polygon", "Polyline", "RadialGrad<PERSON>", "Rect", "Stop", "Svg", "toDataURL", "callback", "options", "ref", "elementRef", "current", "rect", "getBoundingClientRect", "width", "Number", "height", "svg", "document", "createElementNS", "setAttribute", "String", "append<PERSON><PERSON><PERSON>", "cloneNode", "img", "window", "onload", "canvas", "createElement", "context", "getContext", "drawImage", "replace", "src", "encodeSvg", "XMLSerializer", "serializeToString", "Symbol", "TSpan", "Text", "TextPath", "Use", "_default", "default"], "sourceRoot": "../../src", "sources": ["elements.web.ts"], "mappings": ";;;;;;AAoDA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAEO,MAAME,MAAM,SAASC,kBAAQ,CAA0B;EAC5DC,GAAG,GAAG,QAAQ;AAChB;AAACC,OAAA,CAAAH,MAAA,GAAAA,MAAA;AAEM,MAAMI,QAAQ,SAASH,kBAAQ,CAA4B;EAChEC,GAAG,GAAG,UAAU;AAClB;AAACC,OAAA,CAAAC,QAAA,GAAAA,QAAA;AAEM,MAAMC,IAAI,SAASJ,kBAAQ,CAAC;EACjCC,GAAG,GAAG,MAAM;AACd;AAACC,OAAA,CAAAE,IAAA,GAAAA,IAAA;AAEM,MAAMC,OAAO,SAASL,kBAAQ,CAA2B;EAC9DC,GAAG,GAAG,SAAS;AACjB;AAACC,OAAA,CAAAG,OAAA,GAAAA,OAAA;AAEM,MAAMC,OAAO,SAASN,kBAAQ,CAA2B;EAC9DC,GAAG,GAAG,SAAS;AACjB;AAACC,OAAA,CAAAI,OAAA,GAAAA,OAAA;AAEM,MAAMC,aAAa,SAASP,kBAAQ,CAAiC;EAC1EC,GAAG,GAAG,eAAe;AACvB;AAACC,OAAA,CAAAK,aAAA,GAAAA,aAAA;AAEM,MAAMC,mBAAmB,SAASR,kBAAQ,CAE/C;EACAC,GAAG,GAAG,qBAAqB;AAC7B;AAACC,OAAA,CAAAM,mBAAA,GAAAA,mBAAA;AAEM,MAAMC,WAAW,SAAST,kBAAQ,CAA+B;EACtEC,GAAG,GAAG,aAAa;AACrB;AAACC,OAAA,CAAAO,WAAA,GAAAA,WAAA;AAEM,MAAMC,gBAAgB,SAASV,kBAAQ,CAE5C;EACAC,GAAG,GAAG,kBAAkB;AAC1B;AAACC,OAAA,CAAAQ,gBAAA,GAAAA,gBAAA;AAEM,MAAMC,iBAAiB,SAASX,kBAAQ,CAE7C;EACAC,GAAG,GAAG,mBAAmB;AAC3B;AAACC,OAAA,CAAAS,iBAAA,GAAAA,iBAAA;AAEM,MAAMC,iBAAiB,SAASZ,kBAAQ,CAE7C;EACAC,GAAG,GAAG,mBAAmB;AAC3B;AAACC,OAAA,CAAAU,iBAAA,GAAAA,iBAAA;AAEM,MAAMC,cAAc,SAASb,kBAAQ,CAAkC;EAC5EC,GAAG,GAAG,gBAAgB;AACxB;AAACC,OAAA,CAAAW,cAAA,GAAAA,cAAA;AAEM,MAAMC,YAAY,SAASd,kBAAQ,CAAgC;EACxEC,GAAG,GAAG,cAAc;AACtB;AAACC,OAAA,CAAAY,YAAA,GAAAA,YAAA;AAEM,MAAMC,OAAO,SAASf,kBAAQ,CAA2B;EAC9DC,GAAG,GAAG,SAAS;AACjB;AAACC,OAAA,CAAAa,OAAA,GAAAA,OAAA;AAEM,MAAMC,OAAO,SAAShB,kBAAQ,CAA2B;EAC9DC,GAAG,GAAG,SAAS;AACjB;AAACC,OAAA,CAAAc,OAAA,GAAAA,OAAA;AAEM,MAAMC,OAAO,SAASjB,kBAAQ,CAA2B;EAC9DC,GAAG,GAAG,SAAS;AACjB;AAACC,OAAA,CAAAe,OAAA,GAAAA,OAAA;AAEM,MAAMC,OAAO,SAASlB,kBAAQ,CAA2B;EAC9DC,GAAG,GAAG,SAAS;AACjB;AAACC,OAAA,CAAAgB,OAAA,GAAAA,OAAA;AAEM,MAAMC,OAAO,SAASnB,kBAAQ,CAA2B;EAC9DC,GAAG,GAAG,SAAS;AACjB;AAACC,OAAA,CAAAiB,OAAA,GAAAA,OAAA;AAEM,MAAMC,cAAc,SAASpB,kBAAQ,CAAkC;EAC5EC,GAAG,GAAG,gBAAgB;AACxB;AAACC,OAAA,CAAAkB,cAAA,GAAAA,cAAA;AAEM,MAAMC,OAAO,SAASrB,kBAAQ,CAA2B;EAC9DC,GAAG,GAAG,SAAS;AACjB;AAACC,OAAA,CAAAmB,OAAA,GAAAA,OAAA;AAEM,MAAMC,OAAO,SAAStB,kBAAQ,CAA2B;EAC9DC,GAAG,GAAG,SAAS;AACjB;AAACC,OAAA,CAAAoB,OAAA,GAAAA,OAAA;AAEM,MAAMC,WAAW,SAASvB,kBAAQ,CAA+B;EACtEC,GAAG,GAAG,aAAa;AACrB;AAACC,OAAA,CAAAqB,WAAA,GAAAA,WAAA;AAEM,MAAMC,YAAY,SAASxB,kBAAQ,CAAgC;EACxEC,GAAG,GAAG,cAAc;AACtB;AAACC,OAAA,CAAAsB,YAAA,GAAAA,YAAA;AAEM,MAAMC,QAAQ,SAASzB,kBAAQ,CAA4B;EAChEC,GAAG,GAAG,UAAU;AAClB;AAACC,OAAA,CAAAuB,QAAA,GAAAA,QAAA;AAEM,MAAMC,YAAY,SAAS1B,kBAAQ,CAAgC;EACxEC,GAAG,GAAG,cAAc;AACtB;AAACC,OAAA,CAAAwB,YAAA,GAAAA,YAAA;AAEM,MAAMC,kBAAkB,SAAS3B,kBAAQ,CAE9C;EACAC,GAAG,GAAG,oBAAoB;AAC5B;AAACC,OAAA,CAAAyB,kBAAA,GAAAA,kBAAA;AAEM,MAAMC,WAAW,SAAS5B,kBAAQ,CAA+B;EACtEC,GAAG,GAAG,aAAa;AACrB;AAACC,OAAA,CAAA0B,WAAA,GAAAA,WAAA;AAEM,MAAMC,MAAM,SAAS7B,kBAAQ,CAA0B;EAC5DC,GAAG,GAAG,QAAQ;AAChB;AAACC,OAAA,CAAA2B,MAAA,GAAAA,MAAA;AAEM,MAAMC,YAAY,SAAS9B,kBAAQ,CAAgC;EACxEC,GAAG,GAAG,cAAc;AACtB;AAACC,OAAA,CAAA4B,YAAA,GAAAA,YAAA;AAEM,MAAMC,MAAM,SAAS/B,kBAAQ,CAA0B;EAC5DC,GAAG,GAAG,QAAQ;AAChB;AAACC,OAAA,CAAA6B,MAAA,GAAAA,MAAA;AAEM,MAAMC,aAAa,SAAShC,kBAAQ,CAAiC;EAC1EC,GAAG,GAAG,eAAe;AACvB;AAACC,OAAA,CAAA8B,aAAA,GAAAA,aAAA;AAEM,MAAMC,CAAC,SAASjC,kBAAQ,CAAqB;EAClDC,GAAG,GAAG,GAAG;EACTiC,YAAYA,CAACC,KAAyB,EAAE;IACtC,MAAM;MAAEC,CAAC;MAAEC,CAAC;MAAE,GAAGC;IAAK,CAAC,GAAGH,KAAK;IAE/B,IAAI,CAACC,CAAC,IAAIC,CAAC,KAAK,CAACC,IAAI,CAACC,SAAS,EAAE;MAC/BD,IAAI,CAACC,SAAS,GAAG,GAAGH,CAAC,IAAI,CAAC,KAAKC,CAAC,IAAI,CAAC,EAAE;IACzC;IAEA,OAAOC,IAAI;EACb;AACF;AAACpC,OAAA,CAAA+B,CAAA,GAAAA,CAAA;AAEM,MAAMO,KAAK,SAASxC,kBAAQ,CAAyB;EAC1DC,GAAG,GAAG,OAAO;AACf;AAACC,OAAA,CAAAsC,KAAA,GAAAA,KAAA;AAEM,MAAMC,IAAI,SAASzC,kBAAQ,CAAwB;EACxDC,GAAG,GAAG,MAAM;AACd;AAACC,OAAA,CAAAuC,IAAA,GAAAA,IAAA;AAEM,MAAMC,cAAc,SAAS1C,kBAAQ,CAAkC;EAC5EC,GAAG,GAAG,gBAAgB;AACxB;AAACC,OAAA,CAAAwC,cAAA,GAAAA,cAAA;AAEM,MAAMC,MAAM,SAAS3C,kBAAQ,CAA0B;EAC5DC,GAAG,GAAG,QAAQ;AAChB;AAACC,OAAA,CAAAyC,MAAA,GAAAA,MAAA;AAEM,MAAMC,IAAI,SAAS5C,kBAAQ,CAAwB;EACxDC,GAAG,GAAG,MAAM;AACd;AAACC,OAAA,CAAA0C,IAAA,GAAAA,IAAA;AAEM,MAAMC,IAAI,SAAS7C,kBAAQ,CAAwB;EACxDC,GAAG,GAAG,MAAM;AACd;AAACC,OAAA,CAAA2C,IAAA,GAAAA,IAAA;AAEM,MAAMC,OAAO,SAAS9C,kBAAQ,CAA2B;EAC9DC,GAAG,GAAG,SAAS;AACjB;AAACC,OAAA,CAAA4C,OAAA,GAAAA,OAAA;AAEM,MAAMC,OAAO,SAAS/C,kBAAQ,CAA2B;EAC9DC,GAAG,GAAG,SAAS;AACjB;AAACC,OAAA,CAAA6C,OAAA,GAAAA,OAAA;AAEM,MAAMC,QAAQ,SAAShD,kBAAQ,CAA4B;EAChEC,GAAG,GAAG,UAAU;AAClB;AAACC,OAAA,CAAA8C,QAAA,GAAAA,QAAA;AAEM,MAAMC,cAAc,SAASjD,kBAAQ,CAAkC;EAC5EC,GAAG,GAAG,gBAAgB;AACxB;AAACC,OAAA,CAAA+C,cAAA,GAAAA,cAAA;AAEM,MAAMC,IAAI,SAASlD,kBAAQ,CAAwB;EACxDC,GAAG,GAAG,MAAM;AACd;AAACC,OAAA,CAAAgD,IAAA,GAAAA,IAAA;AAEM,MAAMC,IAAI,SAASnD,kBAAQ,CAAwB;EACxDC,GAAG,GAAG,MAAM;AACd;AAACC,OAAA,CAAAiD,IAAA,GAAAA,IAAA;AAEM,MAAMC,GAAG,SAASpD,kBAAQ,CAAuB;EACtDC,GAAG,GAAG,KAAK;EACXoD,SAASA,CACPC,QAAgC,EAChCC,OAA4C,GAAG,CAAC,CAAC,EACjD;IACA,MAAMC,GAAG,GAAG,IAAI,CAACC,UAAU,CAACC,OAAO;IAEnC,IAAIF,GAAG,KAAK,IAAI,EAAE;MAChB;IACF;IAEA,MAAMG,IAAI,GAAG,IAAAC,4BAAqB,EAACJ,GAAG,CAAC;IAEvC,MAAMK,KAAK,GAAGC,MAAM,CAACP,OAAO,CAACM,KAAK,CAAC,IAAIF,IAAI,CAACE,KAAK;IACjD,MAAME,MAAM,GAAGD,MAAM,CAACP,OAAO,CAACQ,MAAM,CAAC,IAAIJ,IAAI,CAACI,MAAM;IAEpD,MAAMC,GAAG,GAAGC,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;IACzEF,GAAG,CAACG,YAAY,CAAC,SAAS,EAAE,OAAOR,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACI,MAAM,EAAE,CAAC;IAC/DC,GAAG,CAACG,YAAY,CAAC,OAAO,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;IACxCG,GAAG,CAACG,YAAY,CAAC,QAAQ,EAAEC,MAAM,CAACL,MAAM,CAAC,CAAC;IAC1CC,GAAG,CAACK,WAAW,CAACb,GAAG,CAACc,SAAS,CAAC,IAAI,CAAC,CAAC;IAEpC,MAAMC,GAAG,GAAG,IAAIC,MAAM,CAAChC,KAAK,CAAC,CAAC;IAC9B+B,GAAG,CAACE,MAAM,GAAG,MAAM;MACjB,MAAMC,MAAM,GAAGT,QAAQ,CAACU,aAAa,CAAC,QAAQ,CAAC;MAC/CD,MAAM,CAACb,KAAK,GAAGA,KAAK;MACpBa,MAAM,CAACX,MAAM,GAAGA,MAAM;MACtB,MAAMa,OAAO,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;MACvCD,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEE,SAAS,CAACP,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7BjB,QAAQ,CAACoB,MAAM,CAACrB,SAAS,CAAC,CAAC,CAAC0B,OAAO,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAC;IACpE,CAAC;IAEDR,GAAG,CAACS,GAAG,GAAG,2BAA2B,IAAAC,gBAAS,EAC5C,IAAIT,MAAM,CAACU,aAAa,CAAC,CAAC,CAACC,iBAAiB,CAACnB,GAAG,CAClD,CAAC,EAAE;EACL;AACF;AAAC9D,OAAA,CAAAkD,GAAA,GAAAA,GAAA;AAEM,MAAMgC,MAAM,SAASpF,kBAAQ,CAA0B;EAC5DC,GAAG,GAAG,QAAQ;AAChB;AAACC,OAAA,CAAAkF,MAAA,GAAAA,MAAA;AAEM,MAAMC,KAAK,SAASrF,kBAAQ,CAAyB;EAC1DC,GAAG,GAAG,OAAO;AACf;AAACC,OAAA,CAAAmF,KAAA,GAAAA,KAAA;AAEM,MAAMC,IAAI,SAAStF,kBAAQ,CAAwB;EACxDC,GAAG,GAAG,MAAM;AACd;AAACC,OAAA,CAAAoF,IAAA,GAAAA,IAAA;AAEM,MAAMC,QAAQ,SAASvF,kBAAQ,CAA4B;EAChEC,GAAG,GAAG,UAAU;AAClB;AAACC,OAAA,CAAAqF,QAAA,GAAAA,QAAA;AAEM,MAAMC,GAAG,SAASxF,kBAAQ,CAAuB;EACtDC,GAAG,GAAG,KAAK;AACb;AAACC,OAAA,CAAAsF,GAAA,GAAAA,GAAA;AAAA,IAAAC,QAAA,GAAAvF,OAAA,CAAAwF,OAAA,GAEctC,GAAG", "ignoreList": []}