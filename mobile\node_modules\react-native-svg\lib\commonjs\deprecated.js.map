{"version": 3, "names": ["showErrorCSS", "name", "type", "Error", "SvgCss", "SvgCssUri", "SvgWithCss", "SvgWithCssUri", "inlineStyles", "LocalSvg", "WithLocalSvg", "loadLocalRawResource"], "sourceRoot": "../../src", "sources": ["deprecated.tsx"], "mappings": ";;;;;;;;;;;;;;AAAO,SAASA,YAAYA,CAACC,IAAY,EAAEC,IAAY,EAAS;EAC9D,MAAMC,KAAK,CACT,iDAAiDD,IAAI,MAAMD,IAAI,oHACjE,CAAC;AACH;AAEO,SAASG,MAAMA,CAAA,EAAU;EAC9BJ,YAAY,CAAC,QAAQ,EAAE,WAAW,CAAC;AACrC;AAEO,SAASK,SAASA,CAAA,EAAU;EACjCL,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC;AACxC;AAEO,SAASM,UAAUA,CAAA,EAAU;EAClCN,YAAY,CAAC,YAAY,EAAE,WAAW,CAAC;AACzC;AAEO,SAASO,aAAaA,CAAA,EAAU;EACrCP,YAAY,CAAC,eAAe,EAAE,WAAW,CAAC;AAC5C;AAEO,SAASQ,YAAYA,CAAA,EAAU;EACpCR,YAAY,CAAC,cAAc,EAAE,UAAU,CAAC;AAC1C;AAEO,SAASS,QAAQA,CAAA,EAAU;EAChCT,YAAY,CAAC,UAAU,EAAE,WAAW,CAAC;AACvC;AAEO,SAASU,YAAYA,CAAA,EAAU;EACpCV,YAAY,CAAC,cAAc,EAAE,WAAW,CAAC;AAC3C;AAEO,SAASW,oBAAoBA,CAAA,EAAU;EAC5CX,YAAY,CAAC,sBAAsB,EAAE,UAAU,CAAC;AAClD", "ignoreList": []}